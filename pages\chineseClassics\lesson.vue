<template>
  <view class="container">
    <!-- 返回按钮和标题 -->
    <view class="nav-header">
      <view class="back-icon" @click="handleBack">
        <image src="/static/icons/arrow_back.png" class="back-image" />
      </view>
      <text class="page-title">{{ pageTitle }}</text>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <image :src="info.cover_photo" class="book-image" mode="aspectFill"/>
      <view class="book-info">
        <text class="book-name">{{ info.title }}</text>
        <text class="book-description">{{ info.sub_title }}</text>
      </view>
    </view>

    <view class="lesson-list">
      <view class="lesson-item" v-for="(item, index) in list" :key="index" @click="handleLessonClick(item)">
        <text class="lesson-title">{{ item.name }}</text>
        <image class="lesson-icon" src="/static/icons/headset.png" />
      </view>
    </view>
	<uni-load-more v-if="totalNum > 0" :status="loadStatus" :content-text="{ contentdown: '加载更多', }" @tap="loadMore"/>
  </view>
</template>

<script>
import http from '/api/index.js'
import Tools from '/utils/index.js'

export default {
  data() {
    return {
      pageTitle: '',
      info: {},
      bookId: '',
      list: [],
      totalNum: 0,
      loadStatus: '',
      dataParams: {
        limit: 20,
        page: 1
      }
    }
  },
  onLoad(options) {
    const { id, title } = options

    this.pageTitle = title || '国学经典'
    this.bookId = id
    this.getList()
  },
  onReachBottom(){
    console.log('页面滚动到底部');
  	this.loadMore()
  },
  methods: {
    handleBack() {
      uni.navigateBack({
        delta: 1
      })
    },
    getList() {
        this.loadStatus = 'loading'
        const params = {
            userkey: uni.getStorageSync('userkey'),
            id: this.bookId,
            ...this.dataParams
        }
        
        console.log("入参：：：", params)
        http.getGuoxueZhangjieList(params).then(res => {
            if (res.statusCode === 200) {
                console.log("出参：：：", res.data)
                const { title, cover_photo, sub_title, lists, total } = res.data.data
                
                this.info = {title, cover_photo, sub_title}
                this.list = this.list.concat(...lists)
                this.totalNum = total
                this.loadStatus = this.list?.length < total ? 'more' : 'noMore';
            }
        })
	},
    loadMore(){
		if (this.loadStatus !== 'noMore') {
			this.dataParams.page += 1;
			console.log("PAGE:::", this.dataParams.page)
			this.getList()
		}
	},
    handleLessonClick(item) {
      Tools.setStorage('CLASSIC_PLAYLIST', this.list)
      uni.navigateTo({
        url: `/pages/chineseClassics/detail?id=${item.id}&title=${item.name}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 0 0 calc(env(safe-area-inset-bottom) + 30rpx);
  background-color: #f8f8f8;
  .lesson-list {
    padding: 28rpx 24rpx 0;
    .lesson-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #fff;
      padding: 24rpx 40rpx;
      border-radius: 20rpx;
      margin-bottom: 28rpx;
      .lesson-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .lesson-icon {
        width: 28rpx;
        min-width: 28rpx;
        height: 28rpx;
      }
    }
  }
}

.nav-header {
    position: sticky;
    top: 0;
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    background-color: #fff;
    z-index: 9999;
    .back-icon {
    display: flex;
    align-items: center;
    padding-right: 20rpx;
    cursor: pointer;
  }

  .back-image {
    width: 40rpx;
    height: 40rpx;
  }

  .page-title {
    flex: 1;
    max-width: 80%;
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.content {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #fff;
  .book-image {
    width: 216rpx;
    height: 232rpx;
    border-radius: 20rpx;
    margin-right: 40rpx;
  }

  .book-info {
    display: flex;
    flex-direction: column;
  }

  .book-name {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 20rpx;
  }
  .book-description {
    font-size: 28rpx;
    color: #666;
  }
}
</style>