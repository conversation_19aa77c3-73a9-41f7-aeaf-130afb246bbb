<template>
	<niceui-navbar class="my-navbar" :backShow="false" title="点读课本">
		<template #content>
			<view class="navbar-right-btn" @click="showGradePopup">
				{{ activeItem.gradeName }}
				<i class="iconfont icon-xia<PERSON><PERSON><PERSON>"></i>
			</view>
		</template>
	</niceui-navbar>

	<view class="page flex-col">
		<!-- Custom Header -->
		<!-- Header with subject tabs -->
		<view class="subject-tabs flex-col">
			<view class="subject-wrapper flex-row center-horizontal">
				<text
					v-for="(subject, index) in subjects"
					:key="index"
					:class="['subject-text', { 'active-subject': activeItem.subjectId === subject.subjectId }]"
					@click="selectSubject(subject)"
				>
					{{ subject.name }}
				</text>
			</view>
		</view>

		<!-- Main content section -->
		<view class="content-section flex-row center-horizontal">
			<!-- Version list on the left -->
			<view class="version-list flex-col">
				<view
					v-for="(item, index) in publishData"
					:key="index"
					:class="['version-item flex-center', { 'active-version': activeItem.publisherId === item.id }]"
					@click="changeSelect(item, 'publisherName')"
				>
					{{ item.aliasName }}
				</view>
			</view>

			<!-- Book list on the right -->
			<view class="book-list">
				<view v-if="bookData.length > 0" class="book-item flex-row" v-for="(item, index) in bookData" :key="index" @longpress="handleLongPress(item)">
					<view v-if="item.isDown" class="is-down">已下载</view>
					<image class="book-cover" :src="item.coverImageUrl" mode="aspectFit" />
					<view class="book-detail">
						<view class="text-group flex-col">
							<text class="grade-text oellipsis">{{ item.name }}</text>
							<text class="term-text">{{ item.subName }}</text>
						</view>

						<view class="button-group">
							<view @click="goBookDetial(item)" class="action-button">点读</view>
							<view v-if="item.subjectId == 'CN'" @click="jumpPage(item)" class="action-button ml-10">笔画</view>
							<view v-if="item.subjectId == 'EN'" @click="jumpPage(item)" class="action-button ml-10">单词听写</view>
						</view>
						<view class="play-count-con">
							<text>播放</text>
							<text class="orange-color play-count">{{ item.playCount }}</text>
							<text @click="addFavorite(item, true)" v-if="!item.favorite" class="orange-color">加入收藏</text>
							<text @click="addFavorite(item, false)" v-else>取消收藏</text>
						</view>
					</view>
				</view>
				<up-empty v-else text="暂无书本数据"></up-empty>
			</view>
		</view>
	</view>

	<!-- Add popup component after the main view -->
	<uni-popup ref="gradePopup" type="bottom">
		<view class="grade-popup">
			<view class="grade-popup-header">
				<text>选择年级</text>
				<text @click="closeGradePopup" class="close-btn">关闭</text>
			</view>
			<view class="grade-list">
				<view v-for="(grade, index) in gradeData" :key="index" :class="['grade-item', { active: activeItem.gradeId === grade.value }]" @click="selectGrade(grade)">
					{{ grade.label }}
				</view>
			</view>
		</view>
	</uni-popup>

	<!-- Add DownLoadTips component -->
	<down-load-tips
		ref="downloadTips"
		:visible="showDownloadTips"
		:bookId="selectedBookId"
		:bookData="selectedBookData"
		@cancel="handleDownloadCancel"
		@success="handleDownloadSuccess"
		@error="handleDownloadError"
	/>
</template>

<script>
import http from '/api/index.js';
import { gradeList } from '@/common/js/index.js';
import Tools from '/utils/index.js';
import DownLoadTips from './components/downLoadTips.vue';

export default {
	components: {
		DownLoadTips
	},
	data() {
		return {
			bookData: [],
			bookList: [],
			gradeData: gradeList,
			publishData: [],
			userId: '',
			activeItem: {
				gradeName: '1年级上',
				gradeId: '1a',
				publisherId: 'PEP',
				publisherName: '人教版',
				subjectId: 'EN'
			},
			subjects: [
				{
					subjectId: 'EN',
					name: '英语'
				},
				{
					subjectId: 'CN',
					name: '语文'
				}
			],
			showDownloadTips: false,
			selectedBookId: '',
			selectedBookData: null,
			downloadTipsRef: 'downloadTips',
			recentOpenBook: Tools.getStorage('recentOpenBook') || []
		};
	},
	mounted() {
		this.init();
	},
	methods: {
		init() {
			this.getCurrentItem();
			// this.login();

			// this.$showAdvert();
		},
		getCurrentItem() {
			const data = Tools.getStorage('bookListActiveItem');
			if (data && data.gradeId) {
				this.activeItem = data;
			}
			this.getBookList();
			this.getPublishers();
		},
		login() {
			var that = this;
			uni.login({
				success: function (loginRes) {
					http.wxLoginApi({
						code: loginRes.code
					}).then((res) => {
						that.userId = res.result.user_id;
						that.$setStorage('openId', res.result.open_id);
					});
				}
			});
		},
		async getBookList() {
			const localBookList = await Tools.getLocalBookList();
			http.getBookList(this.activeItem).then((res) => {
				this.bookData = res.data || [];
				this.bookData = this.bookData.sort((item, nextItem) => {
					return item.sort - nextItem.sort;
				});

				this.bookData.map((item) => {
					item['isDown'] = false;
					if (localBookList.includes(item.id)) {
						item['isDown'] = true;
					}
				});
				Tools.setStorage('bookListActiveItem', this.activeItem);
			});
		},
		getPublishers() {
			http.getPublishers().then((res) => {
				this.publishData = res.data || [];
				this.publishData.map((item) => {
					item.label = item.aliasName;
					item.value = item.id;
				});
				this.publishData.sort((item, nextItem) => {
					return nextItem.sort - item.sort;
				});
			});
		},
		addFavorite(item, flag) {
			const data = {
				bookId: item.id,
				favorite: flag
			};
			http.isFavoriteBook(data).then((res) => {
				item.favorite = flag;
				Tools.toast(flag ? '加入收藏成功' : '取消收入成功');
			});
		},
		addOpenBookCount(item) {
			const data = {
				bookId: item.id
			};

			http.addOpenBookCount(data).then((res) => {
				item.playCount += 1;
			});
		},
		changeSelect(item, name) {
			this.activeItem.publisherId = item.id;
			this.activeItem.publisherName = item.aliasName;
			this.getBookList();
			if (name == 'publisherName') {
				this.activeItem[name] = this.getPublishName();
			} else {
				this.activeItem[name] = this.getGradeName();
			}
		},
		getGradeName() {
			const data = gradeList.find((item) => {
				return item.value == this.activeItem.gradeId;
			});
			return data.label;
		},
		getPublishName() {
			const data = this.publishData.find((item) => {
				return item.id == this.activeItem.publisherId;
			});
			return data.aliasName;
		},
		addRecentOpenBook(item) {
			this.recentOpenBook.unshift(item);
			this.recentOpenBook = Tools.uniqueArrayObjects(this.recentOpenBook, 'id');
			Tools.setStorage('recentOpenBook', this.recentOpenBook);
		},
		deleteRecentOpenBook(data) {
			this.recentOpenBook = this.recentOpenBook.filter((item) => item.id !== data.id);
			Tools.setStorage('recentOpenBook', this.recentOpenBook);
		},
		async goBookDetial(item) {
			// #ifdef APP-PLUS
			const isDownloaded = await Tools.checkIfDownloaded(item.id);
			if (!isDownloaded) {
				if (!item.downloadUrl) {
					Tools.toast('本书无下载链接');
					return;
				}
				// Show download tips
				this.selectedBookId = item.id;
				this.selectedBookData = item;
				this.showDownloadTips = true;
				return;
			}
			// #endif

			Tools.goPage(1, `/pages/book/readBook?bookId=${item.id}`);
			this.addRecentOpenBook(item);
			this.addOpenBookCount(item);
		},
		jumpPage(item) {
			if (item.subjectId == 'EN') {
				Tools.goPage(1, `/pages/tool/wordSpell/unitList?bookId=${item.id}&type=1`);
				return;
			}
			Tools.setStorage('webViewData', {
				src: `http://uni.tbookmaster.com/#/pages/tool/hanziWrite/list?bookId=tape3a_002001  `
			});
			Tools.goPage(1, `/pages/webview/index`);

			// Tools.goPage(1, `/pages/tool/hanziWrite/list?bookId=${item.id}`);
		},
		goTest(item, type = 2) {
			Tools.goPage(1, `/pages/wordSpell/unitList?bookId=${item.id}&publishingId=${item.publishing_id}&bookName=${item.book_name}&type=${type}`);
		},
		changeItem() {
			this.getBookList();
		},
		noProblem() {
			Tools.goPage(1, `/pages/noProblem/noProblem`);
		},
		selectSubject(item) {
			this.activeItem.subjectId = item.subjectId;
			this.getBookList();
		},
		goToAllGrades() {
			// Navigate to the all grades page
			Tools.goPage(1, '/pages/grades/all');
		},
		showGradePopup() {
			this.$refs.gradePopup.open();
		},
		closeGradePopup() {
			this.$refs.gradePopup.close();
		},
		selectGrade(grade) {
			this.activeItem.gradeId = grade.value;
			this.activeItem.gradeName = grade.label;
			this.getBookList();
			this.closeGradePopup();
		},
		handleDownloadCancel() {
			this.showDownloadTips = false;
			this.selectedBookId = '';
			this.selectedBookData = null;
		},
		handleDownloadError(error) {
			console.error('Error downloading:', error);
			this.showDownloadTips = false;
			this.selectedBookId = '';
			this.selectedBookData = null;
		},
		async handleDownloadSuccess() {
			this.showDownloadTips = false;
			uni.showToast({
				title: '正在打开书本',
				icon: 'success',
				duration: 1500
			});
			await Tools.handReadBookJson(this.selectedBookId);
			this.$nextTick(() => {
				this.addOpenBookCount(this.selectedBookData);
				this.addRecentOpenBook(this.selectedBookData);
				Tools.goPage(1, `/pages/book/readBook?bookId=${this.selectedBookId}`);
				this.selectedBookId = '';
				this.selectedBookData.isDown = true;
				this.selectedBookData = null;
			});
		},
		async handleLongPress(item) {
			if (!item.isDown) return;

			// Show confirmation dialog
			uni.showModal({
				title: '删除提示',
				content: '确定要删除该课本吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							// Remove from local storage
							const localBookList = await Tools.getLocalBookList();
							const newBookList = localBookList.filter((id) => id !== item.id);
							await Tools.setStorage('localBookList', newBookList);

							// Delete the downloaded files
							// #ifdef APP-PLUS
							const bookDir = await Tools.getBookStoragePath();
							uni.showLoading({
								title: '正在删除文件...',
								mask: true
							});
							plus.io.resolveLocalFileSystemURL(
								bookDir + item.id,
								(entry) => {
									entry.removeRecursively(
										() => {
											// Update UI
											item.isDown = false;
											uni.hideLoading();
											Tools.removeStorage('bookDetail-' + item.id);
											Tools.getStorage('readBookBeginPage-' + item.id);
											Tools.toast('删除成功');
											this.deleteRecentOpenBook(item);
										},
										(error) => {
											console.error('Delete failed:', error);
											uni.hideLoading();
											Tools.toast('删除失败');
										}
									);
								},
								(error) => {
									console.error('Path resolve failed:', error);
									uni.hideLoading();
									Tools.toast('删除失败');
								}
							);
							// #endif

							// #ifdef MP-WEIXIN
							const bookDir = `${wx.env.USER_DATA_PATH}/${item.id}`;
							const fs = wx.getFileSystemManager();
							fs.rmdir({
								dirPath: bookDir,
								recursive: true,
								success: () => {
									// Update UI
									item.isDown = false;
									Tools.toast('删除成功');
									this.deleteRecentOpenBook(item);
								},
								fail: (err) => {
									console.error('Delete failed:', err);
									Tools.toast('删除失败');
								}
							});
							// #endif
						} catch (error) {
							console.error('Error deleting book:', error);
							Tools.toast('删除失败');
						}
					}
				}
			});
		}
	},
	onShareAppMessage(res) {
		if (res.from === 'button') {
			// 来自页面内分享按钮
			console.log(res.target);
		}
		return {
			title: '教材点读机,帮助小学生趣味学英语,提升英语成绩必备神器'
			// imgUrl:
		};
	}
};
</script>

<style scoped lang="scss">
.page {
	background: #f5f5f5;
	width: 100%;
	padding: 30upx;
	min-height: 100vh;
	box-sizing: border-box;
	// margin-top: 100upx;
}

.custom-header {
	background: #ffffff;
	padding: 10upx 20upx;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	align-items: center;

	.header-title {
		font-size: 32rpx;
		color: #333333;
	}

	.header-button {
		background: #ed9126;
		border-radius: 20rpx;
		padding: 5upx 10upx;

		.header-button-text {
			color: #ffffff;
			font-size: 28rpx;
		}
	}
}

.subject-tabs {
	border-radius: 16upx;
	margin-bottom: 30upx;
	overflow: hidden;
}

.subject-wrapper {
	display: inline-flex;
}

.subject-text {
	width: 332upx;
	height: 64upx;
	line-height: 64upx;
	background: #e9e9e9;
	border-radius: 200upx;
	white-space: nowrap;
	font-weight: 500;
	font-size: 28rpx;
	color: #666666;
	text-align: center;
	margin-right: 16upx;

	&.active-subject {
		background: #ed9126;
		color: #ffffff;
	}
}

.content-section {
	width: 100%;
	box-sizing: border-box;
	overflow: hidden;
	border-radius: 20upx;
	display: flex;
}

.version-list {
	width: 180upx;
	min-height: 80vh ;

	flex: 0 0 180upx;
	background: #e9e9e9;
	border-radius: 20upx 20upx 0 0;
	font-size: 28rpx;
	color: #666666;
	line-height: 33rpx;
	box-sizing: border-box;
}

.version-item {
	text-align: center;
	padding: 40upx 5upx;
	width: 100%;

	&.active-version {
		background: #ffffff;
		color: #ed9126;
	}
}

.book-list {
	background: #ffffff;
	padding: 30upx 30upx 15upx;
	box-sizing: border-box;
	flex: 1;
	min-width: 0;
}

.book-item {
	margin-bottom: 28upx;
	padding: 12upx;
	background: #e9e9e9;
	border-radius: 20rpx;
	position: relative;
	overflow: hidden;

	.book-cover {
		width: 152upx;
		height: 204upx;
		margin-right: 20upx;
		border-radius: 20upx;
	}
	.is-down {
		background-color: #11c490;
		width: 100upx;
		height: 45upx;
		line-height: 45upx;
		border-bottom-right-radius: 35upx;
		font-weight: 400;
		font-size: 20rpx;
		color: #ffffff;
		text-align: center;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 99;
	}

	.book-detail {
		.text-group {
			.grade-text {
				font-weight: 500;
				font-size: 28upx;
				color: #333333;
				max-width: 260upx;
				margin-top: 10upx;
			}

			.term-text {
				font-size: 24upx;
				color: #999999;
				margin: 15upx 0;
			}
		}

		.play-count-con {
			font-size: 20rpx;
			color: #999999;

			.play-count {
				margin: 0 30upx 0 10upx;
			}
		}
	}
}

.action-button {
	padding: 0 20upx;
	height: 40rpx;
	line-height: 40upx;
	text-align: center;
	background: #ed9126;
	border-radius: 200rpx;
	font-weight: 500;
	font-size: 24rpx;
	color: #ffffff;
}

.button-group {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 15upx;
}

/* Utility classes */
.flex {
	&-row {
		display: flex;
		flex-direction: row;
	}

	&-col {
		display: flex;
		flex-direction: column;
	}
}

.justify-between {
	justify-content: space-between;
}

.header-style {
	background-color: #f0f0f0;
	border-bottom: 2px solid #ccc;
	padding: 10upx 0;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.grade-popup {
	background-color: #fff;
	padding: 30upx 30upx 90upx;
	border-radius: 20rpx 20rpx 0 0;

	.grade-popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 1px solid #eee;

		.close-btn {
			color: #999;
			font-size: 28rpx;
		}
	}

	.grade-list {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx 0;

		.grade-item {
			width: 33.33%;
			text-align: center;
			padding: 20rpx 0;
			font-size: 28rpx;
			color: #333;

			&.active {
				color: #ed9126;
			}
		}
	}
}

.navbar-right-btn {
	position: relative;
	z-index: 9999;
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;

	.icon-xiajiantou {
		margin-left: 6rpx;
	}
}
</style>
