<template>
	<div class="student-spell-container">
		<view class="content-con">
			<view class="progress-bar-con">
				<view class="count">{{ progress }}/{{ totalWords }}</view>
				<view class="progress-bar">
					<view class="progress" :style="{ width: progressWidth }"></view>
				</view>
			</view>

			<view class="word-display">
				{{ currentPinyin }}
			</view>
			<img class="voice-img" src="/static/icons/voice.png" alt="" />
			<view class="tips">自动播放，每个词语播放{{playCount}}遍</view>
		</view>

		<view class="bottom-btn-wrap">
			<button class="pause-btn" @tap="nextWord">下一个</button>
		</view>
		<down-num @countdown-complete="handleCountdownComplete" />
	</div>
</template>

<script>
	import DownNum from './components/downNum.vue'
	import Tools from '/utils/index.js'
	export default {
		components: {
			DownNum
		},
		data() {
			return {
				isPlaying: false,
				words: {},
				selectedWords: [],
				showSettings: false,
				innerAudioContext: null,
				currentIndex: 0,
				interval: 3, // Default interval
				playCount: 2, // Default play count
				countValue: 1,
				progress: 1,
				totalWords: 0,
				currentPinyin: ''
			};
		},
		computed: {
			progressWidth() {
				return `${(this.progress / this.totalWords) * 100}%`;
			}
		},
		methods: {
			initData(data) {
				this.loadSettings();
				this.words = data;
				this.totalWords = this.words.selectedWords && this.words.selectedWords.length || 0
				this.currentPinyin = this.words.selectedWords[this.currentIndex].pinyin;
				this.mp3Array = this.words.selectedWords && this.words.selectedWords.length && this.words.selectedWords
					.map(item => item.audio)
			},
			loadSettings() {
				const settings = Tools.getStorage('spellSettings');
				if (settings) {
					this.interval = settings.interval || this.interval;
					this.playCount = settings.playCount || this.playCount;
				}
			},
			// 播放音频的函数
			playAudio(index) {
				this.innerAudioContext.src = this.mp3Array[index];
				this.innerAudioContext.autoplay = true;
				this.innerAudioContext.onCanplay(() => {
					// 当音频可以播放时自动播放
					this.isPlaying = true
					this.innerAudioContext.play();
				});
			},
			startPlayback() {
				this.innerAudioContext = uni.createInnerAudioContext() // 创建一个音频上下文实例
				window.closeAudio = this.stopAction;
				// 监听音频播放结束事件
				this.innerAudioContext.onEnded(() => {
					setTimeout(() => {
						if (this.countValue >= this.playCount) { // 达到设置的播放次数
							this.countValue = 1
							this.currentIndex++; // 切换到下一首音频
							if (this.progress < this.totalWords) {
								this.progress++;
							}
						} else {
							this.countValue++;
						}
						if (this.currentIndex >= this.mp3Array.length) {
							// 如果已经播放到最后一首，跳转结果页
							this.stopAction()
							uni.navigateTo({
								url: '/pages/wordSpell/resultPage'
							})
						} else {
							if (this.isPlaying) {
								this.currentPinyin = this.words.selectedWords[this.currentIndex].pinyin;
								this.playAudio(this.currentIndex); // 播放下一首音频
							}
						}
					}, this.interval * 1000)
				});

				// 初始化播放第一首音频
				this.playAudio(this.currentIndex);
			},
			handleCountdownComplete() {
				this.currentIndex = 0;
				this.startPlayback();
			},
			nextWord() {
				this.innerAudioContext.stop()
				this.countValue = 1;
				this.currentIndex++;
				if (this.progress < this.totalWords) {
					this.progress++;
				}
				if (this.currentIndex >= this.mp3Array.length) {
					// 如果已经播放到最后一首，跳转结果页
					this.stopAction()
					uni.navigateTo({
						url: '/pages/wordSpell/resultPage'
					})
				} else {
					this.currentPinyin = this.words.selectedWords[this.currentIndex].pinyin;
					this.playAudio(this.currentIndex); // 播放下一首音频
				}
			},
			stopAction() {
				console.log('stopAction');
				this.isPlaying = false
				this.innerAudioContext.stop()
				this.innerAudioContext.destroy();
				return true
			}
		}
	};
</script>

<style scoped lang="scss">
	.student-spell-container {
		min-height: 100vh;
		padding: 20px;
		text-align: center;
		background: #f7f7f7;
	}

	.content-con {
		width: 100%;
		box-sizing: border-box;
		padding: 60upx 48upx;
		background-color: #fff;
	}

	.progress-bar-con {
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.count {
		margin-right: 20upx;
	}

	.progress-bar {
		width: 100%;
		background-color: #ffeccc;
		border-radius: 5px;
		height: 10px;
		margin: 20px 0;
	}

	.progress {
		background-color: #f4a316;
		height: 100%;
		border-radius: 5px;
	}

	.word-display {
		margin: 20px 0;
		border-top: 2px solid #f59c12;
		border-bottom: 2px solid #f59c12;
		height: 120upx;
		position: relative;
		line-height: 100upx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 70upx;
		color: #333333;

		&:before {
			content: '';
			width: 100%;
			height: 1upx;
			background-color: #f59c12;
			position: absolute;
			left: 0;
			top: 33%;
		}

		&::after {
			content: '';
			width: 100%;
			height: 1upx;
			background-color: #f59c12;
			position: absolute;
			left: 0;
			top: 66%;
		}
	}

	.voice-img {
		width: 190upx;
		height: 190upx;
	}

	.bottom-btn-wrap {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 40rpx;
		padding: 0 140rpx;
		display: flex;
		justify-content: center;

		.pause-btn {
			width: 100%;
			height: 72rpx;
			background: #f59c12;
			border-radius: 48rpx;
			color: #ffffff;
			font-size: 32rpx;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;
			border: none;

			&::after {
				border: none;
			}

			&:active {
				opacity: 0.8;
			}
		}
	}

	.tips {
		font-size: 14px;
		color: #999999;
	}
</style>