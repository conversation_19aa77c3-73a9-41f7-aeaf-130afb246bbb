### Navbar Props
```
|  属性名   | 类型 |  默认值   | 说明 |
|  ----  | ----  |  ----  | ----  |
| title  | String |  自定义标题  | 页面的标题  |
| titleSize  | Number |  32  | 标题字体大小  |
| titleColor  | String |  #000  | 标题字体颜色  |
| titleShow  | Boolean |  true  | 标题是否显示  |
| backShow  | Boolean |  true  | 回退按钮是否显示  |
| background  | String |  linear-gradient(to right, #56b7fb 0%, #d1e9ff 100%)  | 导航背景色  |
| barHeight  | Number |  32  | 导航高度，配合插槽的使用，插槽高度 = barHeight - 标题行的高度 |
```
### Navbar Slots
```
|  名称   | 说明 |
|  ----  | ----  | 
| content  | 可通过插槽实现头部更多效果 |
```
### Navbar 示例
```
<template>
	<view>
		<!-- 用法一 注释用法二的代码看效果-->
		<niceui-navbar title="不使用插槽模式">
		</niceui-navbar>
		
		<!-- 用法二 注释用法一的代码看效果-->
		<niceui-navbar title="使用插槽模式" barHeight="130">
			<view slot="content" class="custom-content">
				插槽区域
			</view>
		</niceui-navbar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		onLoad(option) {
			
		},
		methods: {
			
		}
	}
</script>

<style>
.custom-content{
	width:100%;
	height:100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 32rpx;
	background: #f0ad4e;
	color:#fff;
}
</style>
```