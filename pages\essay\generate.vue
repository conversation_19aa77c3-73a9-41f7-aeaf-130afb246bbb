<template>
	<view class="essay-generate">
		<!-- 顶部标签切换 -->
		<view class="tab-wrapper">
			<view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', currentTab === index ? 'active' : '']" @tap="switchTab(index)">
				{{ tab }}作文
			</view>
		</view>

		<!-- 主体内容区 -->
		<view class="content">
			<!-- 作文题目 -->
			<view class="section">
				<text class="section-title">作文题目</text>
				<view class="input-box">
					<textarea v-model="formData.title " placeholder="我的爸爸" auto-height />
				</view>
			</view>

			<!-- 写作要求 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">写作要求（选填）</text>
					<!-- <view class="camera-btn" @tap="takePhoto">
            <image src="/static/icons/camera.png" mode="aspectFit" />
            <text>拍照提取</text>
          </view> -->
				</view>
				<view class="input-box">
					<textarea v-model="formData.content" placeholder="示例：幽默的爸爸" auto-height />
				</view>
			</view>

			<!-- 年级选择 -->
			<view class="section select-section">
				<text>选择年级</text>
				<view class="right">
					<text>{{ formData.grade }}</text>
					<uni-icons type="right" size="14"></uni-icons>
					<picker mode="selector" :range="gradeList" range-key="text" @change="handleGradeChange">
						<view class="picker-view">
							{{ formData.grade }}
						</view>
					</picker>
				</view>
			</view>

			<!-- 字数要求 -->
			<view class="section select-section">
				<text>字数要求</text>
				<view class="right">
					<text>{{ formData.zishu }}</text>
					<uni-icons type="right" size="14"></uni-icons>
					<!-- <picker mode="selector" :range="wordCountList" range-key="text" @change="handleWordCountChange">
						<view class="picker-view">
							{{ formData.zishu }}
						</view>
					</picker> -->
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer">
			<button class="generate-btn" @tap="generateEssay">一键成文</button>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { essayStore } from '/stores/essay.js';
import { toast } from "/utils/index.js";
import http from '/api/index.js'
const essay = essayStore();



// 顶部标签数据
const tabs = ['语文', '英语'];
const currentTab = ref(0);

// 表单数据
const formData = reactive({
	usekey:  uni.getStorageSync('userkey'),
	title: "责任与担当",
	content: "积极有为 一代人",
	xueke: "语文",
	wenti: "记叙文",
	type: "2",
	grade: "高中",
	zishu: "800",
	token: ''
});

// 调试输出
console.log('formData:', formData);

// 修改数据结构以适配 picker
const gradeList = reactive([
	{ text: '1年级', value: '1年级' },
	{ text: '2年级', value: '2年级' },
	{ text: '3年级', value: '3年级' },
	{ text: '4年级', value: '4年级' },
	{ text: '5年级', value: '5年级' },
	{ text: '6年级', value: '6年级' },
	{ text: '初中', value: '初中' },
	{ text: '高中', value: '高中' }
]);

const wordCountList = reactive([
	{ text: '300字', value: '300' },
	{ text: '400字', value: '400' },
	{ text: '500字', value: '500' },
	{ text: '600字', value: '600' },
	{ text: '700字', value: '700' },
	{ text: '800字', value: '800' },
	{ text: '900字', value: '900' },
	{ text: '1000字', value: '1000' }
]);

// 处理选择事件
const handleGradeChange = (e) => {
	const index = e.detail.value;
	formData.grade = gradeList[index].value;
};

const handleWordCountChange = (e) => {
	const index = e.detail.value;
	formData.zishu = wordCountList[index].value;
};

// 切换标签
const switchTab = (index) => {
	currentTab.value = index;
	formData.language = tabs[index];
};

// 拍照提取
const takePhoto = () => {
	uni.chooseImage({
		count: 1,
		sourceType: ['camera'],
		success: (res) => {
			// 处理拍照后的图片
			console.log(res.tempFilePaths);
		}
	});
};

// 生成作文
const generateEssay = () => {
	// 实现作文生成逻辑
	if(!formData.title) {
		toast('请输入作文题目');
		return;
	}
	essay.saveEssay(formData)
	uni.navigateTo({
		url: '/pages/essay/blank'
	});
};

</script>

<style scoped>
.essay-generate {
	min-height: 100vh;
	background-color: #f5f7fa;
}

.tab-wrapper {
	display: flex;
	background-color: #fff;
	padding: 0 30rpx;
}

.tab-item {
	position: relative;
	padding: 30rpx 40rpx;
	font-size: 30rpx;
	color: #999;
}

.tab-item.active {
	color: #333;
	font-weight: 500;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background-color: #4080ff;
}

.content {
	padding: 30rpx;
}

.section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 28rpx;
	color: #000;
	font-weight: 400;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	/* margin-bottom: 20rpx; */
}

.section-header .section-title {
	margin-bottom: 0;
	line-height: 1;
}

.camera-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	border-radius: 8rpx;
	background: rgba(64, 128, 255, 0.1);
	height: 32rpx;
	line-height: 32rpx;
}

.camera-btn image {
	width: 36rpx;
	height: 36rpx;
	margin-right: 8rpx;
}

.camera-btn text {
	color: #4080ff;
	font-size: 28rpx;
}

.input-box {
	background-color: transparent;
	padding: 20rpx;
	border-radius: 8rpx;
}

.input-box textarea {
	font-size: 28rpx;
	color: #333;
	background-color: transparent;
	border: none;
	outline: none;
	width: 100%;
	min-height: 32rpx;
	line-height: 1.5;
	-webkit-appearance: none;
}

.input-box textarea::placeholder {
	color: #ebebeb;
}

.select-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	padding: 30rpx 24rpx;
	color: #333;
}

.right {
	position: relative;
}

.right picker {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
}

.picker-view {
	padding: 0 20rpx;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx 30rpx;
	background: #fff;
}

.generate-btn {
	background-color: #4080ff;
	color: #fff;
	border-radius: 45rpx;
	font-size: 32rpx;
	height: 90rpx;
	line-height: 90rpx;
	font-weight: normal;
}
</style>
