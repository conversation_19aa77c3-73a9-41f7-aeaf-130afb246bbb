<template>
  <view class="search-container">
    <!-- 顶部搜索栏 -->
    <view class="search-header">
      <view class="back-icon" @click="goBack">
        <uni-icons type="left" size="20" color="#666"></uni-icons>
      </view>
      <view class="search-input-wrap">
		<image src="/static/icons/search_gray.png" class="search-icon"></image>
        <input 
          type="text" 
          v-model="searchText"
          placeholder="输入关键字/作品"
          placeholder-class="input-placeholder"
          class="search-input"
          confirm-type="search"
          @confirm="handleSearch"
        />
        <uni-icons 
          v-if="searchText"
          type="clear" 
          size="16" 
          color="#999" 
          @click="clearSearchText"
          class="clear-input"
        ></uni-icons>
      </view>
      <view class="search-btn" @click="handleSearch">搜索</view>
    </view>

    <!-- 搜索推荐 -->
    <view class="search-section">
      <text class="section-title recommend">搜索推荐</text>
      <view class="tag-list">
        <view 
          class="tag-item"
          v-for="(item, index) in searchRecommends" 
          :key="index"
          @click="handleRecommendClick(item)"
        >
          {{ item }}
        </view>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="search-section">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <view class="clear-history" @click="clearHistory">
			<image class="icon-delete" src="/static/icons/delete.png" mode=""></image>
        </view>
      </view>
      <view class="tag-list">
        <view 
          class="tag-item"
          v-for="(item, index) in searchHistory" 
          :key="index"
          @click="handleHistoryClick(item)"
        >
          {{ item }}
        </view>
      </view>
    </view>

    <!-- 添加删除确认弹窗 -->
    <view class="delete-modal" v-if="showDeleteModal">
      <view class="modal-mask" @click="closeModal"></view>
      <view class="modal-content">
        <view class="close-btn" @click="closeModal">
          <uni-icons type="closeempty" size="26" color="#999"></uni-icons>
        </view>
        <view class="modal-title">删除</view>
        <view class="modal-text">是否确认删除全部历史记录？</view>
        <view class="modal-btn confirm-btn" @click="confirmDelete">确认</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const searchText = ref('')
const searchRecommends = ref(['文本', '文本文本文本', '文本', '文本文本本文'])
const searchHistory = ref([])
const showDeleteModal = ref(false)

// 从本地存储获取搜索历史
const getSearchHistory = () => {
  try {
    const history = uni.getStorageSync('searchHistory')
    searchHistory.value = history ? JSON.parse(history) : []
  } catch (e) {
    console.error('获取搜索历史失败:', e)
    searchHistory.value = []
  }
}

// 保存搜索历史到本地存储
const saveSearchHistory = (keyword) => {
  const history = searchHistory.value
  const index = history.indexOf(keyword)
  if (index > -1) {
    history.splice(index, 1)
  }
  history.unshift(keyword)
  // 限制历史记录数量为10条
  if (history.length > 10) {
    history.pop()
  }
  
  try {
    uni.setStorageSync('searchHistory', JSON.stringify(history))
    searchHistory.value = history
  } catch (e) {
    console.error('保存搜索历史失败:', e)
  }
}

// 清空搜索历史
const confirmDelete = () => {
  try {
    uni.removeStorageSync('searchHistory')
    searchHistory.value = []
    showDeleteModal.value = false
  } catch (e) {
    console.error('清除搜索历史失败:', e)
  }
}

// 页面加载时获取搜索历史
onMounted(() => {
  getSearchHistory()
})

const goBack = () => {
  uni.navigateBack()
}

const handleSearch = () => {
  if (!searchText.value.trim()) return
  saveSearchHistory(searchText.value)
  // 修改返回逻辑，携带搜索文本
  uni.navigateBack({
    delta: 1,
    success: () => {
      // 使用事件总线传递搜索文本
      uni.$emit('updateSearch', searchText.value)
    }
  })
}

const handleRecommendClick = (item) => {
  searchText.value = item
  handleSearch()
}

const handleHistoryClick = (item) => {
  searchText.value = item
  handleSearch()
}

const clearHistory = () => {
  showDeleteModal.value = true
}

const closeModal = () => {
  showDeleteModal.value = false
}

const clearSearchText = () => {
  searchText.value = ''
}
</script>

<style scoped lang="scss">
.search-container {
  padding: 0;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.search-header {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20rpx 30rpx;
}

.back-icon {
  padding: 10rpx;
  margin-right: 10rpx;
}

.search-input-wrap {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f6f6f6;
  border-radius: 20rpx;
  padding: 0 24rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 37rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
}

.input-placeholder {
  color: #999;
  font-size: 28rpx;
}

.search-btn {
  font-size: 28rpx;
  color: #666;
}

.search-section {
  background-color: #fff;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.recommend {
  display: block;
  margin-bottom: 40rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.tag-item {
  padding: 12rpx 30rpx;
  background-color: #f6f6f6;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #666;
}

.clear-history {
  padding: 10rpx;
  .icon-delete {
	  width: 40rpx;
	  height: 40rpx;
  }
}

.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  
  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
  }
  
  .modal-content {
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 580rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 40rpx 30rpx;
  }
  
  .close-btn {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    padding: 10rpx;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .modal-title {
    font-size: 32rpx;
    color: #333;
    text-align: center;
    margin-bottom: 30rpx;
  }
  
  .modal-text {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }
  
  .confirm-btn {
    height: 72upx;
    line-height:72upx;
    text-align: center;
    font-size: 32upx;
    color: #FFFFFF;
    background: linear-gradient( 102deg, #FFD387 0%, #F4A316 100%);
    border-radius: 80upx;
  }
}

.search-input-wrap {
  .clear-input {
    padding: 10rpx;
    display: flex;
    align-items: center;
  }
}
</style> 