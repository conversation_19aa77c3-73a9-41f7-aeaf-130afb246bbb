<template>
	<div class="download-tips" v-if="visible">
		<div class="tips-content">
			<div class="icon">
				<image class="icon-img" src="/static/images/readBook/downLoadTip.png" alt="notification" />
			</div>
			<div class="title">温馨提示</div>
			<div class="message">点读资源包需要下载或更新</div>
			<div v-if="downloading" class="progress-container">
				<up-line-progress 
					:percentage="downloadProgress" 
					activeColor="#ed9126"
				></up-line-progress>
				<div class="progress-text">
					{{ progressText }}
				</div>
			</div>
			<div v-else class="actions">
				<div class="cancel-btn btn" @click="handleCancel">取消</div>
				<div class="download-btn btn" @click="handleDownload">
					下载/更新
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import Tools from '/utils/index.js';

export default {
	name: 'DownLoadTips',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		bookId: {
			type: [String, Number, null],
			default: null,
			required: false
		},
		bookData: {
			type: Object,
			default: () => ({}),
			required: false
		}
	},
	data() {
		return {
			downloading: false,
			downloadProgress: 0,
			downloadStatus: ''
		};
	},
	computed: {
		progressText() {
			if (this.downloadStatus === 'downloading') {
				return `下载中...`;
			} else if (this.downloadStatus === 'extracting') {
				return '解压中...';
			} else if (this.downloadStatus === 'complete') {
				return '下载完成';
			}
			return '';
		}
	},
	methods: {
		updateProgress(progressData) {
			if (!progressData) return;
			
			this.downloadProgress = progressData.progress || 0;
			this.downloadStatus = progressData.status || '';
		},
		handleCancel() {
			this.$emit('cancel');
		},
		async handleDownload() {
			if (this.downloading) return;
			
			if (!this.bookData?.downloadUrl || !this.bookId) {
				Tools.toast('下载链接不存在');
				return;
			}

			this.downloading = true;
			try {
				// Create a Promise that resolves when download is complete
				const downloadResult = await new Promise((resolve, reject) => {
					Tools.downloadAndExtractZip(
						this.bookData.downloadUrl,
						this.bookId,
						(progress) => {
							this.updateProgress(progress);
							// When progress is 100 and status is complete, resolve the promise
							if (progress.progress === 100 && progress.status === 'complete') {
								resolve();
							}
						}
					).catch(reject);
				});

				// Only emit success after download is fully complete
				this.$emit('success');
			} catch (error) {
				console.error('Download failed:', error);
				Tools.toast('下载失败，请重试');
				this.$emit('error', error);
			} finally {
				this.downloading = false;
				this.downloadProgress = 0;
				this.downloadStatus = '';
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.download-tips {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;

	.tips-content {
		background: #fff;
		border-radius: 12px;
		padding: 20px;
		width: 540rpx;
		text-align: center;
		position: relative;
	}

	.icon {
		width: 120rpx;
		height: 120rpx;
		margin: 0 auto 16px;
		border-radius: 50%;
		position: absolute;
		top: -60rpx;
		left: 50%;
		transform: translateX(-50%);
		.icon-img {
			width: 120rpx;
			height: 120rpx;
		}
	}

	.title {
		font-weight: bold;
		font-size: 32rpx;
		color: #333333;
		margin: 32upx 0;
	}

	.message {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 20px;
	}

	.actions {
		display: flex;
		gap: 12px;
		justify-content: center;

		.btn {
			width: 216rpx;
			height: 72rpx;
			background: #ffffff;
			border-radius: 200rpx;
			border: 2rpx solid #ed9126;
			font-size: 32rpx;
			color: #ed9126;
			text-align: center;
			line-height: 72rpx;

			&.download-btn {
				background: #ed9126;
				color: #ffffff;
			}

			&:disabled {
				opacity: 0.7;
				cursor: not-allowed;
			}
		}
	}

	.progress-container {
		padding: 20rpx;
		
		.progress-text {
			margin-top: 10rpx;
			font-size: 24rpx;
			color: #666;
			text-align: center;
		}
	}
}
</style>
