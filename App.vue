<script>
	export default {
		onLaunch: function() {
			window.closeAudio = function() {
				console.log('init closeAudio')  // 原生app关闭播放器
				return true;
			};
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		created() {
			console.log('created')
			this.saveQueryParamsToStorage()
		},
		onHide: function() {
			console.log('App Hide');
		},
		methods: {
			saveQueryParamsToStorage() {
			    // 获取完整 URL
				const url = window.location.href;
							    
				// 使用正则表达式提取查询字符串
				const regex = /(\?.*?)(#|$)/;
				const match = url.match(regex);
				if (!match) return;
							    
				// 提取查询字符串部分
				const search = match[1];
							    
				// 解析查询字符串
				const queryParams = {};
				const params = search.substring(1).split('&');
				for (const param of params) {
				  const [key, value] = param.split('=');
				  queryParams[key] = decodeURIComponent(value);
				}
				// queryParams.userKey = 'ff22c2008bd4d4b6fc0d949af777e18e' //todo
				// 存储userkey到本地缓存
				uni.setStorageSync('userkey', queryParams.userKey);
				
			}
		}
	}
</script>
<style lang="scss">
	@import "@/uni_modules/uview-plus/index.scss";
	/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
	@import "@/common/css/common.css";
</style>
