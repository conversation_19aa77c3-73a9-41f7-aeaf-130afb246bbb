<template>
	<div class="essay-container">
		<!-- Loading status -->
		<div class="loading-hint" v-if="essayStore.isLoading">好词好句生成中...</div>

		<!-- Essay content -->
		<div class="essay-content">
			<div class="essay-title">
				<h1>{{ currentEssay.topic }}</h1>
				<div class="word-count">{{ markdown.length }}字</div>
			</div>

			<div class="writing-prompt">
				<span class="label">写作要求：</span>
				<span class="prompt-content">{{ currentEssay.requirement }}</span>
			</div>

			<div v-html="wordsContent" class="essay-text"></div>
		</div>

		<!-- Bottom toolbar -->
		<div class="bottom-toolbar">
			<div class="copy-button" @click="copy">复制文章</div>
			<div @click="getWriting" class="new-essay-button">再写一篇</div>
		</div>
	</div>
</template>

<script setup>
	import {
		ref,
		reactive,
		nextTick,
		onMounted,
		onUnmounted
	} from 'vue';
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app';
	import {
		essayStore
	} from '/stores/essay.js';
	import MarkdownIt from 'markdown-it';
	import http from "/api/index.js";
	import { baseUrl } from "/api/request.js";
	const marked = new MarkdownIt();
	const {
		currentEssay
	} = essayStore();
	const markdown = ref('');
	const wordsContent = ref('');
	const writeData = ref('');
	const isEnd = ref(true);
	let eventSource;
	
	

	function scrollToBottom() {
		nextTick(() => {
			const scrollTop = document.documentElement.scrollHeight;
			window.scrollTo(0, scrollTop - 130);
		});
	}

	const openEventSource = () => {
    	const params = new URLSearchParams(currentEssay);
	    const queryString = params.toString();
		console.log(queryString)
// new EventSource(`${baseUrl}/sse/ai/zuowen
// ?${queryString}`, { withCredentials: true });
		eventSource = new EventSource(`/api/sse/ai/zuowen?${queryString}`, { withCredentials: true });

		eventSource.onopen = function() {
			console.log('Connection opened');
		};

		eventSource.onmessage = (e) => {
			console.log(JSON.parse(e.data));
			const data = JSON.parse(e.data);
			console.log(data);
			wordsContent.value += data.choices[0].delta.content.replace("\n", "<br/>");
			scrollToBottom();
		};

		eventSource.addEventListener('close', e => {
			console.log('结速了:', e.data);
			eventSource.close();
		});

		eventSource.onerror = function(err) {
			console.error('EventSource failed:', err);
		};
	};
	const getAiZuoWenToken = () => {
	  const params = {
	    userkey: uni.getStorageSync('userkey')
	  }
	  http.getAiZuoWenToken(params).then(res => {
	    if (res.statusCode === 200) {
			currentEssay.token = res.data.data.token;
			openEventSource();
	    }
	  })
	}
	

	const getWriting = async () => {
		markdown.value = "";
		wordsContent.value = "";
		isEnd.value = false;


		// 发送请求以开始生成作文
		// 这里可以根据需要添加请求逻辑
		getAiZuoWenToken();
	};

	const copy = () => {
		uni.setClipboardData({
			data: wordsContent.value, // 要复制的内容
			success: function() {
				uni.showToast({
					title: '复制成功'
				});
			},
			fail: function() {
				uni.showToast({
					title: '复制失败'
				});
			}
		});
	};

	onMounted(() => {
		// 页面加载时打开 EventSource
		getAiZuoWenToken();
	});

	onUnmounted(() => {
		if (eventSource) {
			eventSource.close();
			console.log('EventSource连接已关闭');
		}
	});

	// onShow(() => {
	// 	openWs();
	// 	console.log('页面显示');
	// });
	onHide(() => {
		console.log('页面隐藏');
	});
</script>

<style scoped>
	.essay-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #fff;
	}

	.loading-hint {
		text-align: center;
		padding: 10px;
		color: #666;
		background-color: #f5f5f5;
	}

	.essay-content {
		flex: 1;
		padding: 20px;
		padding-bottom: 60px;
		overflow-y: auto;
	}

	.essay-title {
		margin-bottom: 20px;
		text-align: center;
	}

	.essay-title h1 {
		font-size: 24px;
		margin-right: 10px;
	}

	.word-count {
		color: #666;
	}

	.writing-prompt {
		margin-bottom: 20px;
	}

	.label {
		color: #666;
	}

	.essay-text {
		width: 100%;
		resize: none;
		font-size: 16px;
		line-height: 1.6;
		padding-bottom: 50px;
	}

	.bottom-toolbar {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 15px;
		border-top: 1px solid #eee;
		gap: 15px;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
	}

	.copy-button,
	.new-essay-button {
		padding: 8rpx 40rpx;
		border-radius: 999rpx;
		border: none;
		font-size: 30rpx;
		min-width: 140rpx;
		/* height: 60rpx; */
		/* line-height: 60rpx; */
		box-sizing: border-box;
	}

	.copy-button {
		background-color: #fff;
		color: #000;
		border: 1px solid #ddd;
	}

	.new-essay-button {
		background-color: #ffd700;
		color: #000;
	}
</style>