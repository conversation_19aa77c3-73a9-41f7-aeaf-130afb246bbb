<template>
  <view :style="{ height: height + 'px'}">
    <view class="head" :style="{ height: height + 'px', background:background}">
      <view class="back" @click="back" v-if="backShow" :style="{ 
				top: top + 'px',
				color:color
			}"> <uni-icons type="back" size="20"></uni-icons> </view>
      <view class="title" v-if="titleShow" :style="{
				top: top + 'px',
				color:titleColor,
				'font-size': titleSize + 'rpx'
			}">{{title}}</view>
			<view   class="bar-content" :style="{ 
				top: top + 'px',
				height:contentHeight + 'px',
			}">
				<slot    name="content"></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: '自定义标题'
			},
			titleSize:{
				type: Number,
				default: 32
			},
			titleColor:{
				type: String,
				default: '#000'
			},
			titleShow: {
				type: Boolean,
				default: true
			},
			backShow: {
				type: Boolean,
				default: true
			},
			background:{
				type: String,
				default: "linear-gradient( 180deg, #98E9DB  0%, #FFFDFB 100%)"
			},
			barHeight:{
				type: Number,
				default: 0
			},
			contentHeight:{
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				height :54,
				top:20,
				contentTop:20,
			}
		},
		created() {
			//设备信息
			let app = uni.getSystemInfoSync();
			console.log('设备信息',app)
			// #ifdef APP-PLUS
			this.top = app.safeArea.top;
			this.height = 65 + app.safeAreaInsets.bottom;
			this.contentTop = app.safeArea.top;
			//#endif
			
			// #ifdef MP-WEIXIN
			
				//胶囊信息
				let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
				this.height  = app.statusBarHeight + menuButtonInfo.height + (menuButtonInfo.top - app.statusBarHeight)*2
				this.top = menuButtonInfo.top;
			//#endif
			
			if(this.contentHeight) {
				this.height = 65 + app.safeAreaInsets.bottom + this.contentHeight;
			}
			if(this.barHeight!=0){
				this.contentTop = this.height
				this.contentHeight = this.barHeight - this.contentTop
				this.height = this.barHeight
				
			}
		},
		methods: {
			back(){
				uni.navigateBack();
				
			}
		}
	}
</script>

<style scoped>
.head{
	position: fixed;
	width: 100%;
	z-index: 9999999;
}
.title{
	position: fixed;
	width: 100%;
	text-align: center;
	/* line-height: 54px; */
}
.back{
	position: fixed;
	width: 81rpx;
	text-align: center;
	/* line-height: 	54px; */
	/* height:  54px; */
	font-size: 32rpx;
	font-weight: 900;
	z-index: 9;
}
.bar-content{
	width: 100vw;
	position: fixed;
}

  .title {
    position: fixed;
    width: 100%;
    text-align: center;
    /* line-height: 54px; */
  }

  .back {
    position: fixed;
    width: 81rpx;
    text-align: center;
    /* line-height: 	54px; */
    /* height:  54px; */
    font-size: 32rpx;
    font-weight: 900;
    z-index: 9;
  }

  .bar-content {
    width: 100vw;
  }
</style>