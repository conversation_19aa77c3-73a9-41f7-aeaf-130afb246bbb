<template>
	<view class="evaluation-mask  "  v-if="visible" @click.stop>
		<view class="evaluation-result animate__animated" :class="{ 'animate__slideInUp': visible }">
			<view class="result-header">
				<text class="title">评测结果</text>
				<text class="close" @click="$emit('closeScoreMask')">×</text>
			</view>
			<view class="result-content">
				<view class="score-item">
					<text class="score">{{ Math.floor(result.suggestedScore) }}</text>
					<text class="label">分</text>
					<uni-icons @click="playTestAudio" class="icon-laba" color="#79cb01;" type="sound-filled" size="26"></uni-icons>
					<!-- <i @click="playTestAudio" class="iconfont icon-laba"></i> -->
				</view>
				<!-- <view class="score-bar">
					<view class="bar" :style="{ width: result.SuggestedScore + '%' }"></view>
				</view> -->
			<!-- 	<view class="score-details">
					<view class="detail-item">
						<text>准确度</text>
						<text>{{ Math.floor(result.pronAccuracy) + '%' }}</text>
					</view>
					<view class="detail-item">
						<text>流利度</text>
						<text>{{ Math.floor(result.pronFluency * 100) + '%' }}</text>
					</view>
					<view class="detail-item">
						<text>完整度</text>
						<text>{{ Math.floor(result.pronCompletion * 100) + '%' }}</text>
					</view>
				</view> -->
				<!-- 逐字评测结果展示 -->
	  
			</view>
			<view class="result-footer">
				<view class="btn" @click="handleRetry">重新录制</view>
				<view class="btn" @click="handleNext">下一句</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		visible: Boolean
	},
	data() {
		return {
			result: {
				suggestedScore: 0,
				pronAccuracy: 0,
				pronFluency: 0,
				pronCompletion: 0
			}
		};
	},
	methods: {
		getResult(data) {
			console.log(data);
			this.result = data;
		},
		getWordClass(score) {
			if (score >= 80) return 'excellent';
			if (score >= 60) return 'good';
			return 'poor';
		},
		handleRetry() {
			this.$emit('retry');
		},
		handleNext() {
			this.$emit('nextTestRead');
		},
		playTestAudio() {
			this.$emit('playTestAudio');
		}
	},
	mounted() {}
};
</script>

<style lang="less" scoped>
.evaluation-mask {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	// background-color: rgba(0, 0, 0, 0);
	z-index: 999;
	pointer-events: none;
}

.evaluation-result {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;
	border-radius: 12px 12px 0 0;
	padding: 20px;
	transform: translateY(100%);
	transition: transform 0.3s ease-out;
	z-index: 1000;
	pointer-events: auto;
	box-sizing: border-box;

	&.slide-in {
		transform: translateY(0);
	}
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;

	.title {
		font-size: 18px;
		font-weight: bold;
	}

	.close {
		font-size: 24px;
		color: #999;
		padding: 5px;
	}
}

.result-content {
	text-align: center;
}

.score-item {
	margin-bottom: 15px;
	color: #ff9645;

	.score {
		font-size: 48px;
		// color: #ff9645;
		font-weight: bold;
	}

	.label {
		font-size: 14px;
		color: #666;
		margin-right: 5px;
	}
	.icon-laba {
			font-size: 36upx;
			font-weight: bold;
		}
		
	.lower-score {
		border: 2rpx solid #fc6861;
		color: #fc6861;
	}
}

.score-details {
	display: flex;
	justify-content: space-around;
	margin: 20px 0;

	.detail-item {
		text-align: center;

		text:first-child {
			font-size: 14px;
			color: #666;
			margin-bottom: 5px;
			display: block;
		}

		text:last-child {
			font-size: 20px;
			color: #333;
			font-weight: bold;
		}
	}
}



.word-details {
	margin: 20px 0;
	line-height: 1.6;

	text {
		margin: 0 2px;

		&.excellent {
			color: #4caf50;
		}

		&.good {
			color: #ff9645;
		}

		&.poor {
			color: #f44336;
		}
	}
}

.result-footer {
	display: flex;
	justify-content: space-between;
	margin-top: 30px;
	padding-bottom: env(safe-area-inset-bottom);

	.btn {
		flex: 1;
		margin: 0 10px;
		height: 40px;
		line-height: 40px;
		text-align: center;
		border-radius: 20px;
		font-size: 16px;
		border: none!important;

		&:first-child {
			background-color: #f5f5f5;
			color: #666;
		}

		&:last-child {
			background-color: #4cd964;
			color: #fff;
		}
	}
}

</style>
