<template>
	<view class="page-con">
		<view class="img-con">
			<image class="des-img" src="/static/1.jpg" mode="widthFix" />
			<!-- <img src="./img/2.jpg" alt=""> -->
			<view class="content-con">
					<view class="price-btn">
						<view class="price-btn-left">
							<view>督&nbsp;&nbsp;&nbsp;学</view>
							<view>早鸟价</view>
						</view>
						<view class="price-btn-right">
							{{pageData.price}}
							<span class="unit">元/月</span>
						</view>
					</view>
					<view class="wechat-con">
						<span class="wechat">详情咨询客服：{{pageData.wechat}}</span>
						<span @click="copyWxCode" class="copy-btn">复制添加</span>
					</view>
			</view>
			<image class="des-img" src="/static/3.jpg" mode="widthFix" />
			<image class="des-img" src="/static/4.jpg" mode="widthFix" />
			<image class="des-img" src="/static/5.jpg" mode="widthFix"/>
		</view>
		
		
		 <view class="document-container">
		    <text class="document-title">课后作业督学服务用户须知</text>
		    
		    <text class="section-title">一、服务次数与时效</text>
		    <text class="content-text">服务总次数：购买后含24次督学服务（每日1次，每次4
		   小时，服务时段为周一至周五17:00-21:00）</text>
		    <text class="content-text">有效期：自购买之日起30天内有效，逾期未使用的服务次
		   数自动失效</text>
		    <text class="content-text">时间计算：每次服务以实际签到开始至结束为准，不足1小时按1小时计算</text>
		    
		    <text class="section-title">二、通勤态度更政策</text>
		    <text class="content-text">不支持退款：服务为定制化教育产品，购买后不因个人原因（如时间冲突、学习效果等）办理退款</text>
		    <text class="content-text">改期规则：需至少提前24小时通过客服申请调整服务日期，每周仅限改期1次，逾期未申请视为正常消耗次数</text>
		    
		    <text class="section-title">三、缺席与请假说明</text>
		    <text class="content-text">缺席处理：未按时参加服务且未提前请假，视为自动放弃当次督学机会，不做补课或费用返还</text>
		    <text class="content-text">请假限制：每月最多可请假2次，超出次数的就席按正常消耗处理</text>
		    
		    <text class="section-title">四、服务规则与权益</text>
		    <text class="content-text">服务形式：通过线上平台（如APP/小程序）进行实时督学，需自备联网设备（手机、笔记本电脑或带话筒的台式机），确保音视频功能正常</text>
		  
		    <text class="content-text">不可转让：服务次数仅限购买人子女使用，不得转赠或出售给他人</text>
		    <text class="content-text">师资安排：机构将根据学员学情匹配督学老师，不支持指定教师（特殊情况可申请调整）</text>
		    
		    <text class="section-title">五、家长配合事项</text>
		    <text class="content-text">设备准备：需为孩子提供稳定网络环境及学习设备（手机/笔记本电脑/带话筒的台式机），提前调试摄像头、麦克风功能，确保视频/语音沟通顺畅</text>
		  
		    <text class="content-text">作业提交：请提前将当日作业内容拍照/文档形式发送至指定渠道，便于老师提前备课</text>
		    
		    <text class="section-title">六、隐私与安全</text>
		    <text class="content-text">机构承诺保护学员个人信息，仅用于督学服务相关沟通，不向第三方泄露</text>
		    <text class="content-text"></text>
		    <text class="content-text">服务过程中产生的学习数据（如作业照片、沟通记录）仅用于教学分析，不作其他用途</text>
		    
		    <text class="section-title">七、其他说明</text>
		    <text class="content-text">如遇系统故障、政策调整等不可抗力因素，机构将提前通知并协商解决方案，不承担额外赔偿责任</text>
		    <text class="content-text">本须知最终解释权归服务提供方所有，购买即视为同意以上全部条款</text>
		  </view>
		 
	
	
	</view>
</template>

<script>
export default {
	data() {
		return {
			pageData: {
				wechat: '',
				price: ''
			}
		}
	},
	methods: {
	   copyWxCode() {
		   if (/(Android)/i.test(navigator.userAgent)) {
		   	//判断Android
		   	callNative.copyWxCode();
		   } else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		   	//判断iPhone|iPad|iPod|iOS
		   	window.webkit.messageHandlers.copyWxCode.postMessage();
		   }
	   }
	},
	onLoad(option){
		this.pageData = option;
		console.log(option);
	}
};
</script>

<style lang="scss" scoped>
	.page-con {
		width: 100%;
		min-height: 100vh;
	}
.img-con {
	width: 100%;
	font-size: 0;
}
.img-con .des-img {
	width: 100%;
	height: 100%;
}
.content-con {
	width: 100%;
	box-sizing: border-box;
	background-color: #78d2fd;
	padding: 15upx 50upx;
	text-align:center;
}

.price-btn {
	width: 100%;
	/* height: 120upx; */
	background: linear-gradient(0deg, #D5F8FF, #FFFFFF);
	border-radius: 56px;
	border: 10upx solid #C3E4FF;
	display: flex;
	justify-content: space-around;
	align-items:center;
	padding: 0;
}
.price-btn-left {
	width: 190upx;
	text-align: center;
	font-weight: bold;
	font-size: 32upx;
	color: #3f7ce3;
	/* line-height: 46upx; */
	 border-right: 1px dashed #3F7CE3;
}
.price-btn-right {
	width: 430upx;
	font-weight: bold;
	font-size: 80upx;
	color: #FB812A;
	text-align: center;
}
.unit {
	font-size: 36upx;
}
.wechat-con {
	height: 60upx;
	line-height: 60upx;
	display: inline-block;
	font-weight: bold;
	font-size: 24upx;
	color: #2A66DE;
	border: 3px solid #2A66DE;
	margin: 20upx auto 10upx;
	border-radius: 30upx;
	overflow: hidden;
}
.wechat {
	padding:0 20upx;
}
.copy-btn {
	 display: inline-block;
	 height: 100%;
	 background: #2A66DE;
	 border-top-left-radius: 29upx;
	 border-bottom-left-radius: 29upx;
	 padding: 0 15upx;
	 color: #BAEBFF;
	 float: right;
}

.document-container {
  padding: 15px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.6;
}

.document-title {
  display: block;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
  color: #000;
}

.section-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin: 15px 0 5px 0;
  color: #000;
}

.content-text {
  display: block;
  font-size: 14px;
  margin-bottom: 5px;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}


</style>
