<template>
	<view class="evaluation-mask  "  v-if="visible" @click.stop>
		<view class="evaluation-result animate__animated" :class="{ 'animate__slideInUp': visible }">
			<view class="result-header">
				<text class="title">背诵成绩</text>
				<text class="close" @click="$emit('closeScoreMask')">×</text>
			</view>
			<view class="result-content">
				<view class="score-item">
					<text :class="getWordClass(result.suggestedScore)" class="score">{{ Math.floor(result.suggestedScore) }}</text>
					<text class="label">分</text>
				</view>
				<view class="play-btn"  @click="playTestAudio">
					<uni-icons color="#267BE5" type="sound-filled" size="26"></uni-icons>
					{{result.isPlay?'暂停播放': '播放背诵'}}
				</view>
				<!-- <view class="score-bar">
					<view class="bar" :style="{ width: result.SuggestedScore + '%' }"></view>
				</view> -->
				<view class="score-details">
					<view class="detail-item">
						<text>错字</text>
						<text>{{ result.errorWordNum}}个</text>
					</view>
					<view class="detail-item">
						<text>提示</text>
						<text>{{  result.tipsNum }}次</text>
					</view>
					<view class="detail-item">
						<text>用时</text>
						<text>{{result.recordingTime }}</text>
					</view>
				</view>
				<!-- 逐字评测结果展示 -->
				<!-- 	<view class="word-details">
					<text v-for="(word, index) in result.words" :key="index" :class="getWordClass(word.score)">
						{{ word.text }}
					</text>
				</view> -->
			</view>
			<view class="result-footer">
				<view class="result-btn" @click="handleRetry">重背一次</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		visible: Boolean
	},
	data() {
		return {
			result: {
				suggestedScore: 0,
				errorWordNum: 0,
				tipsNum: 0,
				recordingTime: '0秒',
				isPlay: false
			},
		};
	},
	methods: {
		getResult(data) {
			console.log(data);
			this.result = data;
		},
		getWordClass(score) {
			if (score >= 80) return 'excellent';
			if (score >= 60) return 'good';
			return 'poor';
		},
		handleRetry() {
			this.$emit('retry');
		},
		handleNext() {
			this.$emit('nextTestRead');
		},
		playTestAudio() {
			this.result.isPlay = !this.result.isPlay;
			this.$emit('playTestAudio',this.result.isPlay);
		}
	},
	mounted() {}
};
</script>

<style lang="less" scoped>
.evaluation-mask {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	// background-color: rgba(0, 0, 0, 0);
	z-index: 999;
	pointer-events: none;
}

.evaluation-result {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	background: #fff;
	border-radius: 12px 12px 0 0;
	padding: 20px;
	transform: translateY(100%);
	transition: transform 0.3s ease-out;
	z-index: 1000;
	pointer-events: auto;
	box-sizing: border-box;

	&.slide-in {
		transform: translateY(0);
	}
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;

	.title {
		font-size: 18px;
		font-weight: bold;
	}

	.close {
		font-size: 24px;
		color: #999;
		padding: 5px;
	}
}

.result-content {
	text-align: center;
}

.score-item {
	margin-bottom: 10px;
	// color: #ff9645;

	.score {
		font-size: 48px;
		// color: #ff9645;
		font-weight: bold;
	}
	.excellent {
		color: #4caf50!important;
	}
	
	.good {
		color: #ff9645!important;
	}
	
	.poor {
		color: red!important;
	}

	.label {
		font-size: 14px;
		color: #666;
		margin-right: 5px;
	}
	.icon-laba {
			font-size: 36upx;
			font-weight: bold;
		}
		
	.lower-score {
		border: 2rpx solid #fc6861;
		color: #fc6861;
	}
}

.score-details {
	display: flex;
	justify-content: space-around;
	margin: 20px 0;

	.detail-item {
		text-align: center;

		text:first-child {
			font-size: 14px;
			color: #666;
			margin-bottom: 5px;
			display: block;
		}

		text:last-child {
			font-size: 20px;
			color: #333;
			font-weight: bold;
		}
	}
}




.result-footer {
	display: flex;
	justify-content: center;
	margin-top: 30px;
	padding-bottom: env(safe-area-inset-bottom);

	.result-btn {
		flex: 1;
		margin: 0 10px;
		height: 40px;
		border-radius: 20px;
		font-size: 16px;
		border: none!important;
		background-color: #267BE5;
		color: #fff;
		text-align: center;
		line-height: 40px;

	}
}
.play-btn {
	width: 124px;
	height: 26px;
	background: #CEE4FF;
	border-radius: 12px;
	text-align: center;
	line-height: 26px;
	color: #267BE5;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 auto;
	
}

</style>
