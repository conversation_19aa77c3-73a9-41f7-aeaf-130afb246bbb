<template>
  <view class="composition-page">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
		<uni-icons type="left" size="30" color="#666" @click="goBack"></uni-icons>
		<!-- <image src="/static/icons/arrow_right.png" class="back-icon" mode="aspectFit" @click="goBack"></image> -->
        <view class="nav-title">{{ navTitle || '作文详情' }}</view>
        <view class="nav-right" >
		     <uni-icons v-if="details.is_bookrack == 0" @click="addBookrack" type="star" size="30" color="#F4A316"></uni-icons>
		     <uni-icons v-else type="star-filled" @click="removeBookrack" size="30" color="#F4A316"></uni-icons>

        </view>
      </view>
    </view>
    
    <!-- 原有内容的容器 -->
    <view class="page-content" :style="{ paddingTop: `calc(${navHeight}px + 20rpx)` }">
		<view class="content-box">
			<view class="title-text">{{ details.title }}</view>
			<view class="content-text">
				<rich-text :nodes="details.content"></rich-text>
			</view>
			<view v-if="details.content && details.content.length" class="word-num">字数：{{ details.content.length }}</view>
		</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Tools from '/utils/index.js'
import http from '/api/index.js'

// 获取状态栏高度
const statusBarHeight = ref(0)
const navHeight = ref(0)
let details = ref({title: '作文素材', content: '作文素材作文素材作文素材作文素材作文素材作文素材作文素材作文素材作文素材作文素材作文素材作文素材作文素材'}), navTitle = ref('')

onLoad((option) => {
	getDetails(option.id)
})

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
  // 导航栏高度：状态栏 + 44px（固定值）
  navHeight.value = systemInfo.statusBarHeight + 44
})

const getDetails = (id) => {
	const params = {
		userkey: uni.getStorageSync('userkey'),
		id,
		type: 1,
	}
	
	console.log("入参：：：", params)
	http.getCompositionDetail(params).then(res => {
		if (res.statusCode === 200) {
			console.log("出参：：：", res.data)
			details.value = res.data.data
		}
	})
}

const addBookrack = () => {
	const params = {
		userkey: uni.getStorageSync('userkey'),
		book_id: details.value.id,
		type: 7,
	}
	
	console.log("入参：：：", params)
	http.addBookrack(params).then(res => {
		details.value.is_bookrack = 1;
		//  提示成功
		uni.showToast({
			title: '收藏成功',
			icon: 'none'
		})
	})
}
const removeBookrack = () => {
	const params = {
		userkey: uni.getStorageSync('userkey'),
		ids: details.value.bookrack_id,
		type: 7,
	} 
	console.log("入参：：：", params)

  
	http.removeBookrack(params).then(res => {
			console.log("出参：：：", res.data)

    details.value.is_bookrack = 0;
		//  提示成功
		uni.showToast({
			title: '取消收藏成功',
			icon: 'none'
		})
	})  
}     


// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

</script>

<style scoped lang="scss">
.composition-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.nav-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 32rpx 0 20rpx;
}

.nav-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 40rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.nav-right {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  transform: rotate(180deg);
}

.page-content {
  position: relative;
  padding: 0 24rpx 24rpx;
}

.title-text {
  margin-bottom: 32rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.content-text {
  font-size: 28rpx;
  color: #888888;
}

.word-num {
  margin-top: 40rpx;
  font-size: 28rpx;
  color: #333333;
}

.content-box {
	padding: 32rpx 24rpx;
	border-radius: 20rpx;
	background-color: #fff;
}
</style>