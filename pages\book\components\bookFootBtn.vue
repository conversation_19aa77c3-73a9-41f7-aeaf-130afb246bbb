<template>
	<view v-if="textType" class="read-footer-con">
	<view v-show="footerStatus == 1" class="read-footer">
		<view @click="abPlay" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/ab.png" mode=""></image>
			<text>复读</text>
		</view>
		<view @click="changePlayMode(2)" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/continue.png" mode=""></image>
			<text>连续</text>
		</view>
		<view v-if="textType == 1" @click="testRead" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/record.png" mode=""></image>
			<text>评测</text>
		</view>
		<view v-if="textType == 1" @click="recite" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/recite.png" mode=""></image>
			<text>背诵</text>
		</view>
	</view>
	<view v-show="footerStatus == 2" class="read-footer">
		<view class="footer-item">
		</view>
		<view v-if="isPlayFlag" @click="isPlayAudio" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/pause.png" mode=""></image>
			<text>暂停</text>
		</view>
		<view v-else="isPlayFlag" @click="isPlayAudio" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/play_btn.png" mode=""></image>
			<text>播放</text>
		</view>
		<view @click="goBack" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/back_btn.png" mode=""></image>
			<text>退出</text>
		</view>
	</view>
	<view v-show="footerStatus == 3" class="read-footer">
		<view v-if="abPlayStep == 1"  class="footer-item ab-tips-con ">
			<image class="ab-img" src="/static/images/readBook/start.png"></image> 
			<span class="tips-text"> 1.点击上方点读区域选择复读起始点</span> 
			<view class="mask-type">复读模式</view>
		</view>
		<view v-if="abPlayStep == 2"  class="footer-item ab-tips-con">
			<image class="ab-img" src="/static/images/readBook/end.png"></image> 
			<span class="tips-text"> 2.点击上方点读区域选择复读结束点</span>
			<view class="mask-type">复读模式</view>
		</view>
		<view v-if="abPlayStep == 3"  class="footer-item">
		</view>
		<view v-if="isPlayFlag && abPlayStep == 3" @click="isPlayAudio" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/pause.png" mode=""></image>
			<text>暂停</text>
		</view>
		<view v-else-if="!isPlayFlag && abPlayStep == 3" @click="isPlayAudio" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/play_btn.png" mode=""></image>
			<text>播放</text>
		</view>
		<view @click="goBack" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/back_btn.png" mode=""></image>
			<text>退出</text>
		</view>
	</view>
	<view v-show="footerStatus == 4 && testReadStep == 1" class="read-footer">
		<view   class="footer-item ab-tips-con ">
			<image class="ab-img" src="/static/images/readBook/start.png"></image> 
			<span class="tips-text">点击上方点读区域选择跟读句子</span> 
			<view class="mask-type">跟读模式</view>
		</view>
		<view @click="goBack" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/back_btn.png" mode=""></image>
			<text>退出</text>
		</view>
	</view>
	<view v-show="footerStatus == 5 && testReadStep == 1" class="read-footer">
		<view   class="footer-item ab-tips-con ">
			<image class="ab-img" src="/static/images/readBook/start.png"></image> 
			<span class="tips-text">点击上方点读区域选择背诵句子</span> 
			<view class="mask-type">背诵模式</view>
		</view>
		<view @click="goBack" class="footer-item">
			<image class="footer-btn" src="/static/images/readBook/back_btn.png" mode=""></image>
			<text>退出</text>
		</view>
	</view>
</view>
</template>
<script>
	import { number } from '../../../uni_modules/uview-plus/libs/function/test';
import Tools from '/utils/index.js';
	// import permission from '@/components/alert/permission.vue';
	export default {
		props: {
			textType: {
				type: Number,
				default: 0
			}
		},
		// components: {
		//   permission
		// },
		data() {
			return {
				footerStatus: 1,  // 1默认 2连续播放 3ab复读
				isPlayFlag: true,
				abPlayStep: 1 , // 1选择开始位置  // 2选择结束位置 //3开始播放
				testReadStep: 1,
				testType: ''
			}
		},
		methods: {
			initData() {
				this.footerStatus = 1;
				this.abPlayStep = 1;
				this.abPlayStep = 1;
				this.isPlayFlag = true;
				this.testReadStep = 1;
				this.$emit('changePlayMode', 1, false, 3, 1)
			},
			changeFooterStatus(num) {
				this.footerStatus = num;
			},
			changePlayMode(num) {
				Tools.audioClick();
				this.footerStatus = num;
				this.$emit('changePlayMode', num)
			},
			changePlayFlag(flag) {
				this.isPlayFlag = flag;
			},
			isPlayAudio() {
				this.isPlayFlag = !this.isPlayFlag;
				this.$emit('isPlayAudio', this.isPlayFlag);
			},
			goBack() {
				Tools.audioClick();
			    this.initData();
				this.$emit('isPlayAudio', false);
			},
			abPlay() {
				Tools.audioClick();
				this.footerStatus = 3;
				this.$emit('changePlayMode', 3, false, 1)
			},
			changeAbPlayStep(num) {
				this.abPlayStep = num;
			},
			showTestShadow() {
				this[this.testType]();
			},
			
		     testRead() { 
				if(Tools.isPermissionsRecordAudioMethod() !=1) {
					this.testType = 'testRead';
					Tools.permissionsRecordAudioMethod();
					return
				}
				 
				// 已获得权限，开始评测
				Tools.audioClick();	
				this.footerStatus = 4;
				this.testReadStep = 1;
				this.$emit('changePlayMode', 4, false, 3, 1);
			},
			recite() {
				if(Tools.isPermissionsRecordAudioMethod() !=1) {
					this.testType = 'recite';
					Tools.permissionsRecordAudioMethod();
					return
				}
				// 已获得权限，开始评测
				Tools.audioClick();
				this.footerStatus = 5;
				this.testReadStep = 1;
				this.$emit('changePlayMode', 5, false, 3, 1);
			},
			changeTestReadStep(num) {
				this.testReadStep = num;
			},
			// 打开应用设置页面
			openAppSettings() {
				// #ifdef APP-PLUS
				if (uni.getSystemInfoSync().platform === 'android') {
					const main = plus.android.runtimeMainActivity();
					const Intent = plus.android.importClass('android.content.Intent');
					const Settings = plus.android.importClass('android.provider.Settings');
					const Uri = plus.android.importClass('android.net.Uri');
					
					const intent = new Intent();
					intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
					const uri = Uri.fromParts('package', main.getPackageName(), null);
					intent.setData(uri);
					main.startActivity(intent);
				} else {
					// iOS
					const UIApplication = plus.ios.import("UIApplication");
					const application = UIApplication.sharedApplication();
					const NSURL = plus.ios.import("NSURL");
					const setting_url = NSURL.URLWithString("app-settings:");
					application.openURL(setting_url);
					plus.ios.deleteObject(setting_url);
					plus.ios.deleteObject(NSURL);
					plus.ios.deleteObject(application);
				}
				// #endif
			}
		},
		mounted() {
			window.showTestShadow = this.showTestShadow;
		}
	}
	
</script>

<style scoped lang="scss">
	.read-footer-con {
		background: transparent;
	}
	.read-footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		// left: 0;
		display: flex;
		justify-content: space-around;
		align-items: center;
		background: linear-gradient(180deg, #fff 0%, #98E9DB 100%);
		padding-bottom: env(safe-area-inset-bottom);
		padding-bottom: 10px;
		padding-top: 10px;
		font-size: 28rpx;
		color: #333333;
		text-align: center;
		.footer-item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			.footer-btn {
				width: 80upx;
				height: 80upx;
				margin-bottom: 8upx;
			}
		}
		
		@property --p {
			syntax: '<percentage>';
			inherits: false;
			initial-value: 0%;
		}
		.tips-text {
			--p:0%;
			background: linear-gradient(to right, #000 var(--p), transparent var(--p) );
			-webkit-background-clip: text;
			background-clip: text;
			color: transparent;
			inset: 0;
			animation: erase1 .5s linear forwards;
			width: 500rpx;
			transform: translateZ(0);
			transform-style: preserve-3d; /* 保留3D空间 */
			
		}
		
		@keyframes erase1 {
			0% {
				--p: 0%;
			}
			100% {
				--p: 100%;
			}
		}
		.ab-tips-con {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			width: 530rpx;
			align-items: center;
			flex-wrap: wrap;
		}
		.ab-img {
			width: 30rpx;
			height: 30rpx;
			// margin-right: 5rpx;
		}
		.mask-type {
			color: #3AC8B0;
			margin-top: 10px;
		}
	}
</style>