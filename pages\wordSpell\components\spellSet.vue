<template>
	<view class="spell-set-mask" :class="{ show: show }" @tap.stop="handleMaskClick">
		<view class="spell-set-content" :class="{ show: show }" @tap.stop>
			<!-- 标题栏 -->
			<view class="header">
				<view class="title-wrap">
					<text class="title">听写设置</text>
				</view>
				<view class="close-icon-wrap" @tap="handleClose">
					<uni-icons class="close-icon" type="down" size="20" color="#999"></uni-icons>
				</view>
			</view>

			<!-- 时间间隔选择 -->
			<view class="setting-group">
				<view class="setting-row">
					<text class="group-label">时间间隔</text>
					<view class="option-group">
						<view v-for="time in timeOptions" :key="time" class="option-item" :class="{ active: selectedTime === time }" @tap="selectTime(time)">
							<text>{{ time }}s</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 播放次数选择 -->
			<view class="setting-group">
				<view class="setting-row">
					<text class="group-label">播放次数</text>
					<view class="option-group">
						<view v-for="count in playCountOptions" :key="count" class="option-item" :class="{ active: selectedCount === count }" @tap="selectCount(count)">
							<text>{{ count }}次</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Tools from '/utils/index.js'

export default {
	data() {
		return {
			timeOptions: [3, 5, 10, 15],
			playCountOptions: [1, 2, 3],
			selectedTime: 3,
			selectedCount: 2,
			show: false
		};
	},
	mounted() {
		// 组件挂载后延迟显示以触发动画
		setTimeout(() => {
			this.show = true;
		}, 50);
	},
	methods: {
		handleClose() {
			this.show = false;
			// 等待动画结束后再关闭，同时传递配置
			Tools.setStorage('spellSettings', {
				interval: this.selectedTime,
				playCount: this.selectedCount
			});
			setTimeout(() => {
				this.$emit('close', {
					interval: this.selectedTime,
					playCount: this.selectedCount
				});
			}, 300);
		},
		handleMaskClick() {
			this.handleClose();
		},
		selectTime(time) {
			this.selectedTime = time;
		},
		selectCount(count) {
			this.selectedCount = count;
		}
	}
};
</script>

<style lang="scss" scoped>
.spell-set-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0);
	z-index: 100;
	display: flex;
	align-items: flex-end;
	transition: background-color 0.3s ease;

	&.show {
		background-color: rgba(0, 0, 0, 0.5);
	}
}

.spell-set-content {
	width: 100%;
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	padding-bottom: env(safe-area-inset-bottom);
	transform: translateY(100%);
	transition: transform 0.3s ease;

	&.show {
		transform: translateY(0);
	}
}

.header {
	position: relative;
	padding: 28rpx 38rpx;
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;

	.title-wrap {
		width: 100%;
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
			font-family: PingFang SC;
			text-align: left;
		}
	}

	.close-icon-wrap {
		width: 40px;
		height: 40px;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		.close-icon {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}
	}
}

.setting-group {
	padding: 0 38rpx;
	margin-bottom: 44rpx;

	.setting-row {
		display: flex;
		align-items: center;
		gap: 30rpx;

		.group-label {
			font-size: 28rpx;
			color: #333;
			font-family: PingFang SC;
			width: 112rpx;
			flex-shrink: 0;
		}

		.option-group {
			display: flex;
			gap: 16rpx;
			flex: 1;

			.option-item {
				height: 66rpx;
				background-color: #f3f3f3;
				border-radius: 66rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex: 1;
				max-width: 120rpx;

				text {
					font-size: 28rpx;
					color: #333;
					font-family: HarmonyOS Sans SC;
				}

				&.active {
					background-color: #ffeccc;
				}
			}
		}
	}
}
</style>
