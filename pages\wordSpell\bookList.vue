<template>
  <view class="book-list-container">
    <view class="content">
      <!-- 左侧科目列表 -->
      <view class="subject-list">
        <view 
          v-for="(subject, index) in subjects" 
          :key="index"
          class="subject-item"
          :class="{ active: currentSubject === subject.category_id }"
          @tap="selectSubject(subject.category_id)"
        >
          <text>{{ subject.category_name }}</text>
        </view>
      </view>

      <!-- 右侧教材列表 -->
      <view class="book-grid-container">
        <view class="book-grid">
          <view 
            class="book-item"
            v-for="(book, index) in books"
            :key="index"
            @tap="selectBook(book)"
            :class="{ active: currentBook === book.id }"
          >
            <image class="book-cover" :src="book.thumb" mode="aspectFit" />
            <text class="book-name">{{ book.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import http from '/api/index.js'
	
export default {	
  data() {
    return {
      currentSubject: 0,
      currentBook: 0,
      subjects: [],
      books: [],
	    bookType: '',
      comeFrom: '',
    }
  },
  onLoad(options) {
  	this.bookType = options.type || ''
    this.comeFrom = options.comeFrom || ''
  },
  created() {
	  this.getBihuaCategory()
  },
  methods: {
    selectSubject(id) {
		this.currentSubject = id
	    this.getBooklists(id)
    },
    selectBook(book) {
      let semester = ''
      
      this.subjects.map(item => {
        if (item.category_id == this.currentSubject) {
          semester = item.category_name
        }
      })
      http.addClick({book_id: book.id}).then(res => {
        let url = `/pages/wordSpell/lesson?grade=${book.name}&semester=${semester}&id=${book.id}&type=${this.bookType}`

        if (this.comeFrom == 'learningGoal') {
          url = `/pages/learningGoal/lesson?grade=${book.name}&semester=${semester}&id=${book.id}`
        }
        if (res.statusCode === 200) {
          uni.navigateTo({
            url
          })
        }
      })
    },
    getBihuaCategory() {
      http.getBihuaCategory().then(res => {
        if (res.statusCode === 200) {
			console.log("出参：：：", res.data)
          this.subjects = res.data.data
          if (this.subjects && this.subjects.length) {
            this.selectSubject(this.subjects[0].category_id)
          }
        }
      })
    },
    getBooklists(category_id) {
		const params = {
			userkey: uni.getStorageSync('userkey'),
			category_id
		}
		console.log("入参：：：", params)
		const APIOPTS = this.comeFrom == 'learningGoal' ? 'getXuexiMubiaoBookList' : 'getBooklists'
		
		http[APIOPTS](params).then(res => {
			if (res.statusCode === 200) {
				console.log("出参：：：", res.data)
				this.books = res.data.data
			}
		})
    }
  }
}
</script>

<style lang="scss" scoped>
.book-list-container {
  min-height: 100%;
  background-color: rgba(247, 247, 247, 1);
  padding: 30rpx;
  box-sizing: border-box;
}

.content {
  display: flex;
  height: calc(100vh - 60rpx - 44px);
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.subject-list {
  width: 160rpx;
 background: #F3F3F3;
  // padding: 20rpx 0;
  
  .subject-item {
    padding: 24rpx 20rpx;
    font-size: 26rpx;
    color: #333;

	
    
    &.active {
      color: #F59C12;
      background-color: #fff;
      position: relative;
      
      &::before {
        // content: '';
        // position: absolute;
        // left: 0;
        // top: 50%;
        // transform: translateY(-50%);
        // width: 6rpx;
        // height: 36rpx;
        // background-color: #F59C12;
        // border-radius: 0 4rpx 4rpx 0;
      }
    }
  }
}

.book-grid-container {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.book-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  
  .book-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx;
	background: #F3F3F3;
	border-radius: 4px;
    
    .book-cover {
      width: 220rpx;
      height: 300rpx;
      margin-bottom: 12rpx;
    }
    
    .book-name {
      font-size: 24rpx;
      color: #333;
    }

    &.active {
      position: relative;
	  background: #fff;
      
      .book-name {
        color: #F59C12;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2rpx solid #F59C12;
        border-radius: 8rpx;
      }
    }
  }
}
</style>
