<template>
  <view class="composition-page">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
		<uni-icons type="left" size="24" color="#666" @click="goBack"></uni-icons>
		<!-- <image src="/static/icons/arrow_right.png" class="back-icon" mode="aspectFit" @click="goBack"></image> -->
        <view class="nav-title">
			<!-- <text class="nav-text">作文·</text> -->
			<picker class="check" @change="bindPickerChange" :value="typeValue" :range="typeList" range-key="name">
			  <view class="check-content">
			  	<view class="uni-input">{{typeList[typeValue]?.name}}</view>
			  	<image class="icon" src="/static/icons/arrow_right.png" mode=""></image>
			  </view>
		  </picker>
		</view>
        <view class="nav-right" @click="goPage('/pages/collect/index')">
		        <uni-icons type="star-filled" size="30" color="#F4A316"></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 原有内容的容器 -->
    <view class="page-content" :style="{ paddingTop: `calc(${navHeight}px + 20rpx)` }">
      <!-- 顶部搜索框 -->
      <view class="search-box" @click="goPage('/pages/search/index')">
		<image src="/static/icons/search.png" class="search-icon"></image>
        <view class="search-input">
          <input type="text" placeholder="搜作文、搜素材" readonly :value="searchValue" placeholder-class="placeholder-style"/>
        </view>
      </view>
      <view class="content-box">
		  <!-- 功能模块区域 -->
		  <view class="feature-grid">
			<view @click="goPage('/pages/essay/generate')" class="feature-item">
			  <image src="/static/ai.png" class="feature-icon"></image>
			</view>
			
			<view class="feature-item">
			  <image src="/static/correction.png" class="feature-icon"></image>
			</view>
		  </view>
      
		  <!-- 四个功能按钮 -->
		  <view class="quick-actions">
			<view class="action-wrapper">
			  <view class="action-item" v-for="(item, index) in actionItems" :key="index" @click="handleActionClick(item)">
				<image :src="item.icon" class="action-icon"></image>
				<text class="action-text">{{item.text}}</text>
			  </view>
			</view>
		  </view>
      
		  <!-- 同步作文区域 -->
		  <view v-if="compositions && compositions.length" class="sync-composition">
			<view class="section-title">
			  <text class="title-text">同步作文</text>
			  <view class="more" @click="goPage('/pages/composition/index')">
				<text>查看全部</text>
				<image src="/static/icons/arrow_right.png" class="arrow-icon"></image>
			  </view>
			</view>
			<view class="composition-list">
			  <view class="composition-item" v-for="(item, index) in compositions" :key="index" @click="goPage(`/pages/composition/detail?id=${item.id}`)">
				<text class="title">{{item.title}}</text>
				<text class="content">{{item.content}}</text>
			  </view>
			</view>
		  </view>
	  </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onBeforeMount, onMounted, onUnmounted } from 'vue'
import Tools from '/utils/index.js'
import http from '/api/index.js'

// 获取状态栏高度
let statusBarHeight = ref(0), navHeight = ref(0), dataParams = reactive({ page: 1, limit: 5 }), compositions = ref([])

let typeValue = ref(0), typeList = ref([
  {id: '1', name: '一年级'},
  {id: '2', name: '二年级'},
  {id: '3', name: '三年级'},
  {id: '4', name: '四年级'},
  {id: '5', name: '五年级'},
  {id: '6', name: '六年级'},
])

const actionItems = ref([
  { icon: '/static/icons/camera.png', text: '拍照润色', id: 'camera' },
  { icon: '/static/icons/great.png', text: '优秀作文', id: 'great' },
  { icon: '/static/icons/material.png', text: '作文素材', id: 'material' },
  { icon: '/static/icons/collect.png', text: '我的收藏', id: 'collect' }
])

let searchValue = ref('')

onBeforeMount(() => {
  // 获取链接中的 userkey 参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$page?.options || {}
  console.log("??", options)
  if (options.userkey) {
    // 存储 userkey 到 localStorage
    uni.setStorageSync('userkey', options.userkey)
  }
  
  getCompositionBookList()
  getSubCategory()
})

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
  // 导航栏高度：状态栏 + 44px（固定值）
  navHeight.value = systemInfo.statusBarHeight + 44

  // 监听搜索文本更新
  uni.$on('updateSearch', (value) => {
    searchValue.value = value
    getCompositionBookList()
  })
})

onUnmounted(() => {
  uni.$off('updateSearch')
})

let category_id = ref('');

const getCompositionBookList = () => {
  const userkey = uni.getStorageSync('userkey')
  const params = {
    userkey,
    category_id: category_id.value,
    search: searchValue.value,
    type: 1,
    ...dataParams
  }
  
  console.log("入参：：：", params)
  http.getCompositionList(params).then(res => {
    if (res.statusCode === 200) {
      console.log("出参：：：", res.data)
      compositions.value = res.data.data.lists
    }
  })
}

const getSubCategory = () => {
  const userkey = uni.getStorageSync('userkey')
  const params = {
    userkey,
  }
  
  http.getSubCategory(params).then(res => {
    if (res.statusCode === 200) {
      console.log("出参22：：：", res.data)
      typeList.value = res.data.data;
    }
  })
}


const bindPickerChange = (e) => {
	typeValue.value = e.detail.value;
	category_id.value = typeList.value[e.detail.value].id;
	getCompositionBookList();
	console.log(e )
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

const handleActionClick = (item) => {
  // 处理点击事件
  switch (item.id) {
  	case 'camera':
	
		break
  	case 'great':
		Tools.goPage(1, `/pages/excellent/index`);
  		break
	case 'material':
		Tools.goPage(1, `/pages/composition/material`);
		break
	case 'collect':
		Tools.goPage(1, `/pages/collect/index`);
		break
  }
}

const goPage = (path) => {
	Tools.goPage(1, path);
}


const goDetail = (id) => {
	Tools.goPage(1, `/pages/composition/detail?id=${id}`)
}
</script>

<style scoped lang="scss">
.composition-page {
  min-height: 100vh;
  background-color: #fff;
  position: relative;
}

.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
}

.nav-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 32rpx 0 20rpx;
}

.nav-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  .check {
  	.check-content {
  		display: flex;
  		align-items: center;
  		justify-content: center;
  	}
  	.icon {
  		width: 30rpx;
  		height: 30rpx;
  		margin-left: 24rpx;
  		transform: rotate(90deg);
  	}
  }
}

.nav-right {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  transform: rotate(180deg);
}

.page-content {
  position: relative;
  padding: 0 0 24rpx;
  background-color: #fff;
  .content-box {
	  padding: 0 24rpx;
  }
}

.search-box {
  display: flex;
  align-items: center;
  padding: 8rpx 48rpx 40rpx 58rpx;
  margin: 16rpx 0 0;
  background: url('/static/search_bg.png') no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  .search-icon {
  	width: 37rpx;
  	height: 36rpx;
  }
  .search-input {
    padding: 16rpx 16rpx 16rpx 34rpx;
	flex: 1;
    display: flex;
    align-items: center;
    background-color: #ffffff;
	::v-deep uni-input {
		width: 100%;
	}
  }
}

.placeholder-style {
  color: #999;
  font-size: 26rpx;
  margin-left: 10rpx;
}

.feature-grid {
  display: flex;
  justify-content: space-between;
  margin-top: 0;
  margin-bottom: 30rpx;
}

.feature-item {
	width: 332rpx;
	height: 160rpx;
	&:first-child {
		margin-right: 20rpx;
	}
	&:last-child {
		margin-left: 20rpx;
	}
	.feature-icon {
		width: 100%;
		height: 100%;
	}
}

.quick-actions {
  margin: 20rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx 0;
}

.action-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.action-item {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
}

.sync-composition {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  background: linear-gradient( 180deg, #FFF8EC 0%, #FFFFFF 87%);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 24rpx;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #F4A316;
}

.more {
  display: flex;
  align-items: center;
  color: #888888;
  font-size: 24rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
}

.composition-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #DDDDDD;
}

.composition-item:last-child {
  border-bottom: none;
}

.composition-item .title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.composition-item .content {
  font-size: 28rpx;
  color: #888888;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style> 