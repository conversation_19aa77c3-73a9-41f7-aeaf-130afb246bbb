<template>
  <view class="composition-page">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
		<uni-icons type="left" size="24" color="#666" @click="goBack"></uni-icons>
		<!-- <image src="/static/icons/arrow_right.png" class="back-icon" mode="aspectFit" @click="goBack"></image> -->
        <view class="nav-title">收藏夹</view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-wrapper" :style="{ marginTop: `${navHeight}px` }">
      <view class="composition-list">
        <view class="composition-item" v-for="(item, index) in compositions" :key="index" @click="goDetail(item.id)">
          <view class="title">{{ item.title }}</view>
          <view class="content">{{ item.content }}</view>
        </view>
        
        <!-- 加载更多提示 -->
        <view class="loading-text" v-if="compositions.length > 0">
          <view v-if="loading">加载中...</view>
          <view v-else>{{ hasMore ? '上拉加载更多' : '没有更多了' }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 修改页面选项的定义方式
export default {
  onReachBottom() {
    const instance = getCurrentInstance()
    // 直接调用 setup 中定义的函数
    instance?.exposed?.onReachBottom?.()
  }
}
</script>

<script setup>
import { ref, onMounted, reactive, getCurrentInstance } from 'vue'
import Tools from '/utils/index.js'
import http from '/api/index.js'

// 获取状态栏高度
const statusBarHeight = ref(0)
const navHeight = ref(0)
const hasMore = ref(true)
const dataParams = reactive({ page: 1, limit: 5 })
// 作文列表数据
const compositions = ref([])
const loading = ref(false)

// 将 loadMore 方法暴露给模板
const onReachBottom = () => {
  console.log('触发加载更多函数', { 
    hasMore: hasMore.value, 
    loading: loading.value,
    currentPage: dataParams.page,
    currentListLength: compositions.value.length 
  })
  
  if (!hasMore.value) {
    
    return
  }
  if (loading.value) {
    console.log('正在加载中，跳过')
    return
  }
  
  console.log('开始加载更多数据，页码：', dataParams.page + 1)
  dataParams.page++
  getCompositionBookList(true)
}

// 暴露方法给模板
defineExpose({
  onReachBottom
})

const getCompositionBookList = (isLoadMore = false) => {
  loading.value = true
  const userkey = uni.getStorageSync('userkey')
  const params = {
    userkey,
    ...dataParams
  }
  
  console.log("入参：：：", params)
  http.getCompositionBookList(params).then(res => {
    if (res.statusCode === 200) {
      console.log("出参：：：", res.data)
      const newList = res.data.data.lists
      if (isLoadMore) {
        compositions.value = [...compositions.value, ...newList]
      } else {
        compositions.value = newList
      }
      hasMore.value = newList.length === dataParams.limit
    }
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
  // 导航栏高度：状态栏 + 44px（固定值）
  navHeight.value = systemInfo.statusBarHeight + 44
  getCompositionBookList()
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

const goDetail = (id) => {
	Tools.goPage(1, `/pages/composition/detail?id=${id}`)
}
</script>

<style scoped lang="scss">
.composition-page {
  background-color: #f5f5f5;
}

.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #fff;
}

.nav-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 32rpx 0 20rpx;
}

.nav-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 40rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  transform: rotate(180deg);
}

// 修改内容区域样式
.content-wrapper {
  background-color: #f5f5f5;
  box-sizing: border-box;
}

.composition-list {
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.composition-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.composition-item .title {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.composition-item .content {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 28rpx;
  color: #888888;
}

.loading-text {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}
</style>