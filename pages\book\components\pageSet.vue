<template>
	<view v-if="isVisible && playSetData" class="page-set-con animate__animated" :class="{ animate__fadeOut: !isSlideIn }">
		<view class="set-con animate__animated" :class="{ animate__slideInUp: isSlideIn, animate__slideOutDown: !isSlideIn }">
			<view class="title">
				<text class="title-text">点读设置</text>
				<uni-icons size="20" @click="closeSettings" type="closeempty" class="close-icon"></uni-icons>
			</view>
			<!-- Playback Speed Slider -->
			<view class="setting-item">
				<text class="label">播放速度</text>
				<view class="slider-container">
					<slider
						:min="0.5"
						:max="1.5"
						:step="0.25"
						:block-size="16"
						:value="playSetData.playbackSpeed"
						:show-value="false"
						activeColor="#79cb01"
						backgroundColor="#E8E8E8"
						block-color="#79cb01"
						@change="updatePlaybackSpeed"
					/>
					<view class="slider-labels">
						<text>0.5x</text>
						<text>0.75x</text>
						<text>1x</text>
						<text>1.25x</text>
						<text>1.5x</text>
					</view>
				</view>
			</view>
			<!-- Time Interval Slider -->
			<!-- <view class="setting-item">
					<text class="label">时间间隔</text>
					<view class="slider-container">
						<slider 
							:min="2" 
							:max="8" 
							:step="2" 
							:block-size="16"
							:value="playSetData.timeInterval"
							:show-value="false"
							activeColor="#F5A623"
							backgroundColor="#E8E8E8"
							block-color="#F5A623"
							@change="updateTimeInterval"
						/>
						<view class="slider-labels">
							<text>2s</text>
							<text>4s</text>
							<text>6s</text>
							<text>8s</text>
						</view>
					</view>
				</view> -->
			<!-- Reading Frequency Slider -->
			<!-- 	<view class="setting-item">
					<text class="label">朗读次数</text>
					<view class="slider-container">
						<slider 
							:min="1" 
							:max="3" 
							:step="1" 
							:block-size="16"
							:value="playSetData.readingCount"
							:show-value="false"
							activeColor="#F5A623"
							backgroundColor="#E8E8E8"
							block-color="#F5A623"
							@change="updateReadingCount"
						/>
						<view class="slider-labels">
							<text>1次</text>
							<text>2次</text>
							<text>3次</text>
						</view>
					</view>
				</view> -->
			<!-- Toggle for Translation -->
			<!-- <view class="setting-item">
					<text>翻译</text>
					<up-switch v-model="playSetData.translationEnabled" inactiveColor="rgb(230, 230, 230)" space="2" activeColor="#F5A623"></up-switch>
				</view> -->
			<!-- Toggle for Reading Mark -->
			<!-- <view class="setting-item">
					<text>点读标记</text>
					<up-switch v-model="playSetData.markEnabled" inactiveColor="rgb(230, 230, 230)" space="2" activeColor="#F5A623"></up-switch>
				</view> -->
			<!-- Toggle for Reading Area -->
			<view class="setting-item">
				<text>点读区域</text>
				<!-- <switch :checked="playSetData.areaEnabled" color="#FFCC33" style="transform:scale(0.7)"/> -->
				<up-switch v-model="playSetData.areaEnabled" inactiveColor="rgb(230, 230, 230)" space="2" activeColor="#79cb01"></up-switch>
			</view>
			<!-- Toggle for Enlarging Content -->
			<view class="setting-item">
				<text>点读时内容放大</text>
				<up-switch v-model="playSetData.enlargeContent" inactiveColor="rgb(230, 230, 230)" space="2" activeColor="#79cb01"></up-switch>
			</view>
			<!-- <view class="setting-item">
					<text>长句断句</text>
					<up-switch v-model="playSetDa ta.longSentenceBreak" inactiveColor="rgb(230, 230, 230)" space="2" activeColor="#F5A623"></up-switch>
				</view> -->
			<!-- Toggle for Playing Original Sound -->
			<!-- <view class="setting-item">
				<text>跟读的时候先播放原声</text>
				<up-switch v-model="playSetData.playOriginalSound" inactiveColor="rgb(230, 230, 230)" space="2" activeColor="#79cb01"></up-switch>
			</view> -->
		</view>
	</view>
</template>

<script>
import Tools from '/utils/index.js';
export default {
	data() {
		return {
			isVisible: true, // 控制页面显示
			isSlideIn: true,
			playSetData: Tools.getStorage('playSetData')
		};
	},
	methods: {
		closeSettings() {
			this.isSlideIn = false;
			setTimeout(() => {
				this.isSlideIn = true;
				this.isVisible = false; // 隐藏页面
			    Tools.setStorage('playSetData', this.playSetData);
				this.$emit('close');
			}, 400);
		},
		updatePlaybackSpeed(e) {
			this.playSetData.playbackSpeed = e.detail.value;
		},
		updateTimeInterval(e) {
			this.playSetData.timeInterval = e.detail.value;
		},
		updateReadingCount(e) {
			this.playSetData.readingCount = e.detail.value;
		}
	}
};
</script>

<style lang="scss" scoped>
.page-set-con {
	background: rgba(0, 0, 0, 0.5);
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	z-index: 99999999;

	.set-con {
		width: 100%;
		background: #ffffff;
		border-radius: 16upx 16upx 0px 0px;
		position: absolute;
		bottom: 0;
		border-top: 2rpx solid #e9e9e9;
		padding: 0 30upx;
		padding-bottom: env(safe-area-inset-bottom);
		box-sizing: border-box;
		font-size: 28rpx;
		color: #333333;
		animation-duration: 0.5s; /* don't forget to set a duration! */
	}
	.title {
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30upx;
		border-bottom: 1rpx dashed #e9e9e9;
		font-size: 32rpx;
		color: #292929;
	}
	.title-text {
		font-size: 20px;
		text-align: center;
		flex: 1;
	}
	.close-icon {
		cursor: pointer;
		position: absolute;
		right: 15upx;
		top: 50%;
		transform: translateY(-50%);
	}
	.setting-item {
		position: relative;
		padding: 20upx 30upx;
		margin: 10upx 0;
		display: flex;
		align-items: center;
		border-bottom: 1rpx dashed #e9e9e9;
		justify-content: space-between;
		height: 80upx; /* 设置固定高度 */

		.label {
			width: 180upx;
			flex-shrink: 0;
			font-size: 28upx;
		}

		.slider-container {
			flex: 1;
			padding-left: 60upx;

			slider {
				margin: 0;
				width: 100%;
			}

			.slider-labels {
				display: flex;
				justify-content: space-between;
				padding: 0 0;
				font-size: 20rpx;
				color: #79cb01;
			}
		}
	}
}

/* 添加滑入滑出效果 */
.slide-enter-active,
.slide-leave-active {
	transition: transform 0.3s ease;
}
.slide-enter,
.slide-leave-to {
	transform: translateY(100%);
}
</style>
