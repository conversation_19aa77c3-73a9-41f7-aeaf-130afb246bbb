<template>
  <view class="excellent-page">
    <!-- 返回按钮和标题区域 -->
    <view class="header">
	  <view class="header-box">
		<uni-icons type="left" size="24" color="#666" @click="goBack"></uni-icons>
		<!-- <image src="/static/icons/arrow_right.png" class="back-icon" mode="aspectFit"  @click="goBack"></image> -->
		<text class="title">优秀作文</text>
	  </view>
	  <view class="check" @click="toggleCheckBox">
		<view class="uni-input">{{ currentCategory }}</view>
		<image class="icon" src="/static/icons/arrow_right.png" mode=""></image>
		<view class="check-box" :class="{'fade-out': !checkBoxVisible}">
			<text v-for="(i, index) in checkList" :key="index" class="check-text" :class="{'check-active': checkActive == index}" @click.stop="checkAction(i,index)">{{ i.name }}</text>
		</view>
	  </view>
    </view>

    <!-- 改回普通view，移除scroll-view -->
    <view class="composition-list">
      <view class="composition-item" v-for="(item, index) in compositions" :key="index" @click="goDetail(item.id)">
        <view class="title-box">
		  <image class="icon-badge" src="/static/icons/badge1.png" mode=""></image>
		  <view class="comp-title">{{ item.title }}</view>
		</view>
        <view class="comp-content">{{ item.content }}</view>
      </view>
      
      <!-- Add loading status text -->
      <view class="loading-text" v-if="loadingStatus !== 0">
        {{ loadingStatus === 1 ? '加载中...' : '没有更多了' }}
      </view>
    </view>

    <view v-if="checkBoxVisible" class="mask" @click.stop="hideCheckBox"></view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import Tools from '/utils/index.js'
import http from '/api/index.js'

let checkBoxVisible = ref(false),category_id=ref(), checkActive = ref(0), checkList = ref(['全部', '写人', '写景', '读后感', '写物', '抒情', '想象', '童话', '书信', '议论文']), dataParams = reactive({ page: 1, limit: 5 })

// Add loading status: 0-hidden, 1-loading, 2-no more
const loadingStatus = ref(0)
let compositions = ref([])

// 添加当前选中的文体文案
const currentCategory = ref('文体')

const getCompositionBookList = (isLoadMore = false) => {
  if (loadingStatus.value === 1) return // Prevent multiple requests
  
  loadingStatus.value = 1
  const userkey = uni.getStorageSync('userkey')
  const params = {
    userkey,
    category_id: category_id.value,
    search: '',
    type: 1,
    ...dataParams
  }
  
  console.log("入参：：：", params)
  http.getCompositionList(params).then(res => {
    if (res.statusCode === 200) {
      const newList = res.data.data.lists
      if (isLoadMore) {
        compositions.value = [...compositions.value, ...newList]
      } else {
        compositions.value = newList
      }
      
      // Update loading status
      loadingStatus.value = newList.length < dataParams.limit ? 2 : 0
      
      // If we got less items than requested, there are no more items
      if (newList.length === dataParams.limit) {
        dataParams.page++
      }
    }
  }).catch(() => {
    loadingStatus.value = 0
  })
}
getCompositionBookList()

const getCompositionCategory = () => {
	http.getCompositionCategory({
		userkey: uni.getStorageSync('userkey')
	}).then(res => {
		if (res.statusCode === 200) {
			if (res.data.data && res.data.data.length) {
				 res.data.data.forEach(item => {
					 if(item.id==4) {
						 checkList.value = item.children;
					 }
				 })
				
			}
		}
	})
}
getCompositionCategory();

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 跳转到详情页
const goDetail = (id) => {
	Tools.goPage(1, `/pages/composition/detail?id=${id}`)
}

const toggleCheckBox = () => {
	checkBoxVisible.value = !checkBoxVisible.value
}

const hideCheckBox = () => {
	checkBoxVisible.value = false
}

const checkAction = (item, index) => {
  category_id.value = item.id
  checkActive.value = index
  currentCategory.value = item.name // 更新选中的文体文案
  dataParams.page = 1 // Reset page number
  checkBoxVisible.value = false // 选择后关闭下拉框

  getCompositionBookList() // Reload list
}

// 使用onReachBottom
onReachBottom(() => {
  console.log('触发上拉加载') // 添加日志
  if (loadingStatus.value === 0) {
    getCompositionBookList(true)
  }
})
</script>

<style scoped lang="scss">
.excellent-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 0 32rpx 0 20rpx;
  z-index: 100;
  .header-box {
	  display: flex;
	  align-items: center;
	  height: 88rpx;
  }
  .check {
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	.uni-input {
		font-size: 30rpx;
		color: #333333;
	}
	.icon {
		width: 30rpx;
		height: 30rpx;
		margin-left: 24rpx;
		transform: rotate(90deg);
	}
	.check-box {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		// height: 460rpx;
		padding: 32rpx 24rpx 0;
		background-color: #fff;
		position: absolute;
		top: 88rpx;
		left: -32rpx;
		right: -32rpx;
		z-index: 999;
		// transition: height 0.4s ease;
		overflow: hidden;
		.check-text {
			width: 212rpx;
			height: 72rpx;
			line-height: 72rpx;
			margin-bottom: 32rpx;
			background-color: #EFEFEF;
			border-radius: 80rpx;
			font-size: 28rpx;
			color: #333333;
			text-align: center;
			opacity: 1;
			transition:  opcity 1s ease 1s;
			&:nth-child(3n+2) {
				margin: 0 32rpx 32rpx;
			}
			&.check-active {
				background-color: #FFF8EC;
				color: #F4A316;
			}
		}
		&.fade-out {
			height: 0;
			padding: 0;
			.check-text {
				height: 0;
				margin: 0;
				opacity: 0;
			}
		}
	}
  }
}
.mask {
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.50);
	z-index: 99;
}
.back-icon {
    width: 40rpx;
    height: 40rpx;
	transform: rotate(180deg);
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
}

.composition-list {
  padding: 184rpx 16rpx 30rpx; // 恢复原来的顶部内边距
}

.composition-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 8rpx;
}

.title-box {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.icon-badge {
  width: 32rpx;
  min-width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.comp-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.comp-content {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 28rpx;
  color: #888888;
}

.loading-text {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}
</style> 