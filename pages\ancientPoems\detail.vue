<template>
	<view class="container">
		<!-- 返回按钮和标题 -->
		<view class="nav-header">
			<view class="back-icon" @click="handleBack">
				<image src="/static/icons/arrow_back.png" class="back-image" />
			</view>
			<view class="page-title">
				<text v-for="(tab, index) in tabs" :key="index" class="page-title-item" :class="{ active: activeTab === index }" @click="switchTab(index)">
					{{ tab }}
				</text>
			</view>
			<view class="operation-icon" :class="{ gray: isPlaying }">音</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<view v-if="activeTab === 0 || activeTab === 1">
				<view class="main-title">{{ content?.tiltle }}</view>
				<view class="author">{{ content?.dynasty }} · {{ content?.author }}</view>
				<view v-if="!isRecording" v-for="(item, index) in content?.origTxt" :key="index + 'a'" class="text-content">
					<view class="text-content-item" :style="{ minHeight: isAnyTooltipVisible(index) ? '200rpx' : 'auto' }">
						<view
							class="char-column"
							:class="{ highlight: !isRecording && isHighlighted(char, index) }"
							v-for="(char, charIndex) in item.replace(/[^\u4e00-\u9fa5]/g, '').split('')"
							:key="charIndex"
							@click="toggleAnnotation(char, index, charIndex)"
						>
							<text v-if="activeTab === 0 && !isRecording" class="pinyin-item">{{ content?.pinyin[index]?.split(' ')[charIndex] }}</text>
							<text class="text-item">{{ char }}</text>
							<!-- Display annotation note if visible -->
							<view v-if="isTooltipVisible(index, charIndex) && getAnnotationNote(char)" class="tooltip">
								{{ getAnnotationNote(char) }}
							</view>
						</view>
						<!-- 添加标点符号 -->
						<view class="char-column" v-if="item.match(/[^\u4e00-\u9fa5]+$/)">
							<text v-if="activeTab === 0 && !isRecording" class="pinyin-item"></text>
							<text class="text-item">{{ item.match(/[^\u4e00-\u9fa5]+$/)[0] }}</text>
						</view>
					</view>
					<view v-if="activeTab === 1" class="text-content-item translate">{{ content?.transTxt[index].translate }}</view>
				</view>
				<!-- 开始背诵 -->
				<view v-else v-for="(item, index) in recordData" :key="index" class="text-content">
					<view class="text-content-item">
						<view v-show="subItem.dp_type == 0 || isShowTips" v-for="subItem in item" class="char-column">
							<text :style="{ color: subItem.score < 60 && subItem.dp_type == 0 ? 'red' : subItem.dp_type == 1 && isShowTips ? 'grey' : '' }">
								{{ subItem.char }}
							</text>
						</view>
						<view v-show="item[item.length - 1].dp_type == 0">
							<text class="text-item">{{ index % 2 ? '。' : '，' }}</text>
						</view>
					</view>
				</view>
			</view>
			<view v-else-if="activeTab === 2">
				<view class="author-section">
					<view class="author-header">
						<image class="author-image" :src="content?.authorTxt?.avatar" />
						<view class="author-info">
							<text class="author-name">{{ content?.authorTxt?.name }}</text>
							<text class="author-dynasty">【{{ content?.authorTxt?.dynasty }}】</text>
						</view>
					</view>
					<view class="author-intro">
						<text class="author-intro-title">简介</text>
						<text class="author-intro-content">{{ content?.authorTxt?.profile }}</text>
					</view>
				</view>
				<view class="works-section">
					<view class="works-title">
						作品
						<text class="works-title-count">（{{ content?.authorTxt?.opus_total }}篇）</text>
					</view>
					<view v-for="(work, index) in content?.authorTxt?.opus_list" :key="index" class="work-item" @click="handleWorkItem(work)">
						<image class="work-image" src="/static/icons/gushi.png" />
						<view class="work-info">
							<text class="work-title">{{ work.title }}</text>
							<text class="work-description">{{ work.content }}</text>
						</view>
					</view>
				</view>
			</view>
			<view v-else-if="activeTab === 3">
				<!-- 赏析内容 -->
				<view class="analysis" v-for="(item, index) in content?.analyseTxt" :key="index">{{ item }}</view>
			</view>
		</view>
		<view v-if="activeTab === 0 || activeTab === 1" class="btns" :class="{ 'recording-mode': isRecording }">
			<template v-if="!isRecording">
				<view class="btns-item" @click="handlePlay">
					<image :src="isPlaying ? '/static/icons/play_pause2.png' : '/static/icons/play2.png'" class="btns-item-image" />
					<text class="btns-item-text">{{ isPlaying ? '暂停' : '播放' }}</text>
				</view>
				<view v-if="activeTab === 0" class="btns-item" @click="startRecorder">
					<image src="/static/icons/voice2.png" class="btns-item-image" />
					<text class="btns-item-text">背诵</text>
				</view>
			</template>

			<!-- 录音中的界面 -->
			<template v-else>
				<view class="recording-btn" @click="handleRecite">
					<text class="btns-item-text">背诵完成</text>
				</view>
				<view class="recording-wave">
					<view class="wave-bar" v-for="n in 5" :key="n"></view>
				</view>
				<view class="recording-time">
					<text>{{ totalTime }}</text>
				</view>
				<view class="recording-wave right">
					<view class="wave-bar" v-for="n in 5" :key="n"></view>
				</view>
				<view @click="showTips" class="recording-btn">
					<text class="btns-item-text">{{ isShowTips ? '取消提示' : '提示' }}</text>
				</view>
			</template>
		</view>

		<evaluationResult
			@playTestAudio="playTestAudio"
			@retry="retry"
			@closeScoreMask="closeScoreMask"
			v-if="isEvaluating"
			:visible="isEvaluating"
			ref="scoreMask"
		></evaluationResult>
	</view>
</template>

<script>
import { Howl, Howler } from 'howler';
import http from '/api/index.js';
import Tools from '/utils/index.js';
import { pinyin } from 'pinyin-pro';
import evaluationResult from './evaluationResult.vue';

export default {
	components: {
		evaluationResult
	},
	data() {
		return {
			id: '',
			orgContent: {}, // 原始数据(不带拼音 )
			content: {}, // 处理后的数据(带拼音)
			tooltips: {}, // Store visibility of tooltips for each character
			tabs: ['原文', '译文', '作者', '赏析'],
			activeTab: 0, // Default to the first tab
			// 添加音频相关的数据
			sound: null,
			isPlaying: false,
			isRecording: false,
			recordingTime: 0,
			recordingTimer: null,
			recordData: [],
			origRecordData: [],
			isShowTips: false,
			isEvaluating: false,
			tipsNum: 0
		};
	},
	computed: {
		totalTime() {
			return Tools.removeMilliseconds(Tools.formatTime(this.recordingTime)) || '00:00';
		}
	},
	onLoad(options) {
		const { id } = options;

		this.id = id;
		this.getDetail();
		window.getRecordData = this.getRecordData;
		window.overRecord = this.overRecord;
		window.closeAudio =  () => {
			this.sound.pause();
		}
	},
	methods: {
		showToast(message) {
			uni.showToast({
				title: message,
				icon: 'none'
			});
		},
		handleBack() {
			uni.navigateBack({
				delta: 1
			});
		},
		// 将汉字转换为拼音
		convertToPinyin(text) {
			// Filter out non-Chinese characters
			const chineseText = text.replace(/[^\u4e00-\u9fa5]/g, '');
			return pinyin(chineseText, {
				toneType: 'symbol', // 使用声调符号，如 ā á ǎ à
				type: 'array' // 返回数组格式
			}).join(' '); // Ensure pinyin is joined with spaces
		},
		getDetail() {
			const params = {
				userkey: uni.getStorageSync('userkey') || "709cbec8f29b4ddd84572306ae4a729a",
				id: this.id
			};

			console.log('入参：：：', params);
			http.getGushiDetail(params).then((res) => {
				if (res.statusCode === 200) {
					console.log('出参：：：', res.data);
					this.orgContent = res.data.data;
					this.content = res.data.data;
					// 生成拼音数据
					if (this.content.origTxt) {
						this.content.pinyin = this.content.origTxt.map((text) => this.convertToPinyin(text));
						this.initRecordData();
					}
					this.initHowler();
				}
			});
		},
		initRecordData() {
			this.content.origTxt.forEach((item, index) => {
				this.recordData[index] = [];
				for (let i = 0; i < item.length - 1; i++) {
					this.recordData[index].push({
						dp_type: 1,
						char: item[i],
						dur: 0,
						score: 0,
						end: 0,
						start: 0
					});
				}
			});
		},
		isHighlighted(char, index) {
			return this.content.annotate?.some((annotation) => {
				if (annotation.words.length > 1) {
					return this.content.origTxt[index].includes(annotation.words) && annotation.words.includes(char);
				} else {
					return annotation.words[0] === char;
				}
			});
		},
		toggleAnnotation(char, index, charIndex) {
			let charNote = this.getAnnotationNote(char);
			if (!charNote || !this.isHighlighted(char, index)) {
				return;
			}

			const key = `${index}-${charIndex}`;
			for (let i in this.tooltips) {
				if (this.tooltips[i] == charNote && i != key) {
					this.$set(this.tooltips, i, !this.tooltips[i]);
					return;
				}
			}
			if (!this.tooltips[key]) {
				this.$set(this.tooltips, key, charNote);
			} else {
				this.$set(this.tooltips, key, false);
			}
		},
		isTooltipVisible(index, charIndex) {
			const key = `${index}-${charIndex}`;
			return !!this.tooltips[key];
		},
		isAnyTooltipVisible(index) {
			return Object.keys(this.tooltips).some((key) => key.startsWith(`${index}-`) && this.tooltips[key]);
		},
		getAnnotationNote(char) {
			const annotation = this.content.annotate?.find((annotation) => annotation.words.includes(char));
			return annotation ? annotation.note : '';
		},
		switchTab(index) {
			this.activeTab = index;
			// Scroll to top when switching tabs
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0
			});
			if (index != 0) {
				this.closeScoreMask();
			}
		},
		handleWorkItem(work) {
			uni.navigateTo({
				url: `/pages/ancientPoems/detail?id=${work.id}`
			});
		},
		initHowler() {
			this.sound = new Howl({
				src: [this.content.audio],
				html5: true,
				onplay: this.playSound,
				onend: this.pauseSound,
				onstop: this.pauseSound,
				onpause: this.pauseSound,
				onloaderror: function () {
					console.error('音频加载失败');
				},
				onplayerror: function () {
					console.error('播放失败');
					sound.once('unlock', function () {
						this.sound.play();
					});
				}
			});
		},
		playSound() {
			this.isPlaying = true;
		},
		pauseSound() {
			this.isPlaying = false;
		},
		// 播放音频
		handlePlay() {
			if (!this.content?.audio) {
				this.showToast('暂无音频');
				return;
			}

			if (this.isPlaying) {
				this.sound.pause();
			} else {
				this.sound.play();
			}
		},
		async startRecorder() {
			this.sound.pause();
			this.startRecordingTimer();
			this.isRecording = true;

			window.getRecordData = this.getRecordData;
			window.overRecord = this.overRecord;
			const data = this.content.origTxt.join('');
			console.log(data, '执行startBs方法,传送诗词数据');
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				callNative.startBs(data);
				console.log('startBs方法已经执行');
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.startBs.postMessage(data);
			}
		},
		getRecordData(res) {
			this.recordData = [];
			this.origRecordData = res;
			const singleLength = this.content.origTxt[0].length - 1;
			// 创建二维数组
			for (let i = 0; i < res.length; i += singleLength) {
				this.recordData.push(res.slice(i, i + singleLength));
			}
		},

		// 背诵完成
		async handleRecite() {
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				callNative.bsCompletedAndResult();
				console.log('bsCompletedAndResult方法已经执行');
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.bsCompletedAndResult.postMessage();
			}
		},
		overRecord(res) {
			this.stopRecordingTimer();
			let num = 0;
			this.origRecordData.forEach((item) => {
				if (item.score < 60) {
					num += 1;
				}
			});
			const data = {
				suggestedScore: res,
				errorWordNum: num,
				tipsNum: this.tipsNum,
				recordingTime: Tools.formatTime(this.recordingTime, 1),
				isPlay: false
			};
			this.isEvaluating = true;
			this.$nextTick(() => {
				this.$refs.scoreMask.getResult(data);
			});

			console.log('overRecord方法执行', JSON.stringify(res));
		},
		retry() {
			this.isEvaluating = false;
			this.tipsNum = 0;
			this.isShowTips = false;
			this.initRecordData();
			this.isRecording = true;
			this.stopPlayCurrentBs();
			this.startRecorder();
		},
		closeScoreMask() {
			this.isEvaluating = false;
			this.tipsNum = 0;
			this.isShowTips = false;
			this.initRecordData();
			this.isRecording = false;
			this.stopPlayCurrentBs();
		},
		showTips() {
			this.isShowTips = !this.isShowTips;
			if (this.isShowTips) {
				this.tipsNum += 1;
			}
		},

		// 添加录音计时器相关方法
		startRecordingTimer() {
			this.recordingTime = 0;
			this.recordingTimer = setInterval(() => {
				this.recordingTime++;
			}, 1000);
		},

		stopRecordingTimer() {
			if (this.recordingTimer) {
				clearInterval(this.recordingTimer);
				this.recordingTimer = null;
			}
			// this.recordingTime = 0;
		},
		playTestAudio(flag) {
			if (flag) {
				if (/(Android)/i.test(navigator.userAgent)) {
					//判断Android
					callNative.playCurrentBs();
				} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
					//判断iPhone|iPad|iPod|iOS
					window.webkit.messageHandlers.playCurrentBs.postMessage();
				}
				return;
			}
			this.stopPlayCurrentBs();
		},
		stopPlayCurrentBs() {
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				callNative.stopPlayCurrentBs();
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.stopPlayCurrentBs.postMessage();
			}
		}
	},
	// 组件销毁时清理资源
	onUnload() {
		if (this.sound) {
			this.sound.unload();
			this.sound = null;
		}
		this.stopPlayCurrentBs();

		this.stopRecordingTimer();
	}
};
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	padding: 0 0 calc(env(safe-area-inset-bottom) + 30rpx);
	background-color: #f8f8f8;
}
.nav-header {
	position: sticky;
	top: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
	background-color: #fff;
	z-index: 9999;
	.back-icon {
		display: flex;
		align-items: center;
		padding-right: 20rpx;
		cursor: pointer;
	}

	.back-image {
		width: 40rpx;
		height: 40rpx;
	}

	.page-title {
		flex: 1;
		max-width: 80%;
		margin: 0 50rpx;
		display: flex;
		justify-content: space-around;
		.page-title-item {
			font-size: 28rpx;
			color: #999;
			position: relative;
			&.active {
				color: #267be5;
				font-weight: 500;
				&:after {
					content: '';
					display: block;
					width: 100%;
					height: 6rpx;
					border-radius: 44rpx;
					background-color: #267be5;
					position: absolute;
					bottom: -10rpx;
					left: 0;
				}
			}
		}
	}

	.operation-icon {
		width: 48rpx;
		height: 48rpx;
		line-height: 48rpx;
		border-radius: 50%;
		font-size: 28rpx;
		color: #fff;
		text-align: center;
		background-color: #267be5;
		&.gray {
			background-color: #cdcdcd;
		}
	}
}

.content {
	padding: 40rpx 24rpx 260rpx;
	.main-title {
		font-size: 40rpx;
		color: #333;
		font-weight: 500;
		text-align: center;
		margin-bottom: 16rpx;
	}
	.author {
		font-size: 28rpx;
		color: #999;
		text-align: center;
		margin-bottom: 32rpx;
	}
	.text-content {
		margin-bottom: 48rpx;
		padding: 0 24rpx;
		.text-content-item {
			display: flex;
			justify-content: center;
			align-items: flex-start; // Align items to the top
			&.translate {
				margin-top: 18rpx;
				font-size: 24rpx;
				font-weight: 500;
				color: #999;
			}

			.char-column {
				display: flex;
				flex-direction: column;
				align-items: center;
				min-width: 75rpx; // Further increase width for more space
				padding: 0 10rpx;
				position: relative; // Ensure tooltips are positioned relative to the character
				&:last-child {
					width: 10rpx;
					min-width: unset;
					padding: 0;
				}
				&.highlight {
					border-bottom: 2rpx solid #267be5;
					.pinyin-item {
						color: #267be5; // Highlight color for annotated pinyin
						font-weight: bold;
					}
					.text-item {
						color: #267be5; // Highlight color for annotated characters
						font-weight: bold;
					}
				}
			}

			.pinyin-item {
				height: 36rpx;
				font-size: 24rpx;
				color: #999;
				line-height: 1.4;
				text-align: center;
			}

			.text-item {
				font-size: 32rpx;
				line-height: 1.6;
				text-align: center;
			}
		}
	}
}
.tooltip {
	background-color: #e4f0ff;
	padding: 10rpx;
	border-radius: 8rpx;
	margin-top: 16rpx;
	font-size: 24rpx;
	color: #333;
	position: absolute;
	top: 100%; // Position below the character
	left: 50%;
	transform: translateX(-50%);
	z-index: 1000;
	min-width: 200rpx;
	max-width: 600rpx; // Set a max width for the tooltip
	white-space: normal; // Allow text to wrap
	word-wrap: break-word; // Break long words
	box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1); // Add shadow for better visibility
}
.btns {
	position: fixed;
	bottom: env(safe-area-inset-bottom);
	left: 0;
	width: 100%;
	padding: 20rpx 0;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-evenly;

	&.recording-mode {
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 20rpx;

		.recording-wave {
			width: 120rpx;
			height: 52rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 16rpx;

			.wave-bar {
				width: 10rpx;
				background-color: #267be5;
				border-radius: 8rpx;
				opacity: 0.6;
				animation: waveLeft 1.2s ease-in-out infinite;

				&:nth-child(1) {
					height: 20rpx;
					animation-delay: 0s;
				}
				&:nth-child(2) {
					height: 40rpx;
					animation-delay: 0.3s;
				}
				&:nth-child(3) {
					height: 32rpx;
					animation-delay: 0.6s;
				}
				&:nth-child(4) {
					height: 52rpx;
					animation-delay: 0.9s;
				}
				&:nth-child(5) {
					height: 15rpx;
					animation-delay: 0.9s;
				}
			}

			&.right .wave-bar {
				animation: waveRight 1.2s ease-in-out infinite;

				&:nth-child(1) {
					height: 15rpx;
					animation-delay: 0.9s;
				}
				&:nth-child(2) {
					height: 52rpx;
					animation-delay: 0.6s;
				}
				&:nth-child(3) {
					height: 32rpx;
					animation-delay: 0.3s;
				}
				&:nth-child(4) {
					height: 40rpx;
					animation-delay: 0s;
				}
				&:nth-child(5) {
					height: 20rpx;
					animation-delay: 0.9s;
				}
			}
		}

		.recording-time {
			min-width: 66rpx;
			padding: 0 20rpx;
			color: #999;
			font-size: 24rpx;
			font-weight: 500;
		}

		.recording-btn {
			width: 138rpx;
			min-width: 128rpx;
			height: 72rpx;
			line-height: 72rpx;
			margin: 0 20rpx;
			border-radius: 60rpx;
			background-color: #267be5;
			color: #fff;
			font-size: 28rpx;
			font-weight: 500;
		}
	}

	.btns-item {
		width: 262rpx;
		height: 72rpx;
		background-color: #267be5;
		border-radius: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.btns-item-image {
			width: 28rpx;
			height: 28rpx;
			margin-right: 20rpx;
			display: block;
			flex-shrink: 0;
		}

		.btns-item-text {
			font-size: 28rpx;
			color: #ffffff;
			font-weight: 500;
			line-height: 1;
			display: block;
		}
	}
}

@keyframes waveLeft {
	0% {
		transform: scaleX(1);
		opacity: 0.4;
	}
	50% {
		transform: scaleX(0.8) translateX(-2rpx);
		opacity: 0.8;
	}
	100% {
		transform: scaleX(1);
		opacity: 0.4;
	}
}

@keyframes waveRight {
	0% {
		transform: scaleX(1);
		opacity: 0.4;
	}
	50% {
		transform: scaleX(0.8) translateX(2rpx);
		opacity: 0.8;
	}
	100% {
		transform: scaleX(1);
		opacity: 0.4;
	}
}

.author-section {
	.author-header {
		display: flex;
		align-items: center;
		.author-image {
			width: 196rpx;
			min-width: 196rpx;
			height: 288rpx;
			border-radius: 8rpx;
			margin-right: 40rpx;
		}
		.author-info {
			display: flex;
			flex-direction: column;
			.author-name {
				font-size: 40rpx;
				font-weight: 500;
				color: #333;
			}
			.author-dynasty {
				font-size: 28rpx;
				color: #999;
			}
		}
	}
	.author-intro {
		display: flex;
		flex-direction: column;
		margin-top: 40rpx;
		.author-intro-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
		.author-intro-content {
			line-height: 48rpx;
			margin-top: 20rpx;
			font-size: 28rpx;
			color: #333;
			text-indent: 2em;
		}
	}
}

.works-section {
	margin-top: 40rpx;
	.works-title {
		margin-bottom: 20rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		.works-title-count {
			font-size: 28rpx;
			font-weight: 400;
			color: #999;
		}
	}
	.work-item {
		display: flex;
		align-items: center;
		margin-bottom: 32rpx;
		.work-image {
			width: 196rpx;
			min-width: 196rpx;
			height: 176rpx;
			margin-right: 32rpx;
		}
		.work-info {
			display: flex;
			flex-direction: column;
			.work-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
			}
			.work-description {
				margin-top: 20rpx;
				font-size: 24rpx;
				color: #267be5;
			}
		}
	}
}
.analysis {
	line-height: 48rpx;
	font-size: 28rpx;
	color: #333;
}
</style>