<template>
  <view class="word-list">
    <down-num @countdown-complete="handleCountdownComplete" />
    
    <!-- 课文信息区域 -->
    <view class="lesson-info">
      <view class="lesson-title-wrap">
        <text class="lesson-title">{{ words.class_name }}</text>
      </view>
      
      <!-- 词数和操作区 -->
      <view class="word-count-bar">
        <text class="word-count">共 {{ words.selectedWords && words.selectedWords.length }} 词</text>
      </view>

      <!-- 词语网格 -->
      <view class="word-grid">
        <view 
          v-for="(wordObj, index) in words.selectedWords" 
          :key="index"
          class="word-item"
		   :class="{ selected: currentIndex == index}"
        >
          <view class="pinyin-row">
            <view v-for="(py, idx) in wordObj.pinyin" :key="idx" class="pinyin">{{ py }}</view>
          </view>
          <view class="word-content">
            <view class="hanzi-row">
              <view v-for="(hz, idx) in (wordObj.word || wordObj.phrase)" :key="idx" class="hanzi">{{ hz }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn-wrap">
      <button class="pause-btn" @tap="togglePlay">
        {{ isPlaying ? '暂停播放' : '开始播放' }}
      </button>
    </view>
	
  </view>
</template>

<script>
import DownNum from './components/downNum.vue'
import Tools from '/utils/index.js'
export default {
  components: {
    DownNum
  },
  data() {
    return {
		words: {},
		selectedWords: [],
		showSettings: false,
		isPlaying: false,
		currentIndex: 0,
		interval: 3, // Default interval
		playCount: 2 ,// Default play count
		innerAudioContext: null,
		countValue: 1,
    }
  },
  methods: {
	initData(data) {
		this.loadSettings();
		this.words = data;
		this.mp3Array = this.words.selectedWords && this.words.selectedWords.length && this.words.selectedWords.map(item => item.audio)
		console.log("???", this.words, this.mp3Array)
	},
    loadSettings() {
      const settings = Tools.getStorage('spellSettings');
      if (settings) {
        this.interval = settings.interval || this.interval;
        this.playCount = settings.playCount || this.playCount;
      }
    },
	// 播放音频的函数
	playAudio(index) {
	  this.innerAudioContext.src = this.mp3Array[index];
	  this.innerAudioContext.autoplay = true;
	  this.innerAudioContext.onCanplay(() => {
	    // 当音频可以播放时自动播放
		this.isPlaying = true
		this.innerAudioContext.play();
	  });
	},
	startPlayback() {
		this.innerAudioContext = uni.createInnerAudioContext() // 创建一个音频上下文实例
	    window.closeAudio = this.stopAction;
		// 监听音频播放结束事件
		this.innerAudioContext.onEnded(() => {
			setTimeout(() => {
				if(this.countValue >= this.playCount) { // 达到设置的播放次数
					this.countValue = 1
					this.currentIndex++; // 切换到下一首音频
				} else {
					this.countValue++;
				}
			  if (this.currentIndex >= this.mp3Array.length) {
				// 如果已经播放到最后一首，跳转结果页
				this.stopAction()
				uni.navigateTo({
					url: '/pages/wordSpell/resultPage'
				})
			  } else {
				if (this.isPlaying) {
					this.playAudio(this.currentIndex); // 播放下一首音频
				}
			  }
			}, this.interval*1000)
		});
		
		// 初始化播放第一首音频
		this.playAudio(this.currentIndex);
	},
    handleCountdownComplete() {
        this.currentIndex = 0; 
        this.startPlayback();
    },
	togglePlay() {
		if (this.innerAudioContext.paused) { // 暂停状态，继续播放
			this.isPlaying = true
			this.innerAudioContext.play()
		} else {
			this.isPlaying = false
			this.innerAudioContext.pause()
		}
	},
	stopAction() {
		console.log('stopAction');
		this.isPlaying = false;
		this.innerAudioContext.stop();
		this.innerAudioContext.destroy();
		return true;
	}
  }
}
</script>

<style lang="scss" scoped>
.word-list {
  min-height: 100vh;
  background-color: #F7F7F7;
  padding: 30rpx 24rpx;
  padding-bottom: 160rpx;
}

.lesson-info {
  background-color: #FFF;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
}

.lesson-title-wrap {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .lesson-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    line-height: 32rpx;
  }
}

.word-count-bar {
  display: flex;
  justify-content:center;
  align-items: center;
  margin-bottom: 40rpx;

  .word-count {
    flex: 1;
    font-size: 24rpx;
    color: #999;
	text-align: center;
  }

}

.word-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 28rpx;

  .word-item {
    flex-grow: 0;
    flex-shrink: 0;
    transition: all 0.2s ease;
	margin-bottom: 15upx;

    .word-content {
      display: flex;
      flex-direction: column;
      align-items: center;
	  line-height: 42rpx;
	  background-color: #F3F3F3;
	  border-radius: 20rpx;
	  padding: 16rpx 34rpx;
	  min-width: 70rpx;
    }

    .pinyin-row {
      display: flex;
      gap: 12rpx;
      margin-bottom: 4rpx;
	  justify-content: center;
      
      .pinyin {
        font-size: 24rpx;
        color: #666;
        line-height: 1.2;
		text-align: center;
      }
    }

    .hanzi-row {
      display: flex;
      gap: 12rpx;
      
      .hanzi {
        font-size: 36rpx;
        color: #333;
        font-family: 'Kaiti TC';
        font-weight: normal;
      }
    }
	
	&.selected {
		.word-content {
			background-color: #F59C12!important;
			.hanzi {
				color: #fff !important;
			}
		}
		.pinyin {
		  color: #F59C12;
		}
    }
  }
}

.bottom-btn-wrap {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 40rpx;
  padding: 0 140rpx;
  display: flex;
  justify-content: center;
  
  .pause-btn {
    width: 100%;
    height: 72rpx;
    background: #F59C12;
    border-radius: 48rpx;
    color: #FFFFFF;
    font-size: 32rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    
    &::after {
      border: none;
    }
    
    &:active {
      opacity: 0.8;
    }
  }
}

</style>
