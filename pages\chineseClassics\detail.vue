<template>
	<view class="container">
		<!-- 返回按钮和标题 -->
		<view class="nav-header">
			<view class="back-icon" @click="handleBack">
				<image src="/static/icons/arrow_back.png" class="back-image" />
			</view>
			<text class="page-title">{{ pageTitle }}</text>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<view v-for="(item, index) in content" :key="index" class="text-content"
				:class="{active: item.activeStatus}" @click="jumpToSentence(index)">
				{{ item.content }}
			</view>
		</view>
		<view v-if="audio" class="audio-controls">
			<!-- 音频控制条的实现 -->
			<view class="audio-times">
				<view class="progress-bar">
					<slider class="slider" :value="currentValue" :step="0.01" min="0" :max="maxValueTime || 60"
						@change="sliderChange" activeColor="#1DCE01" backgroundColor="#ECECEC" block-color="#1DCE01"
						block-size="10" />
				</view>
				<view class="audio-times-bottom">
					<text class="current-time">{{ currentTime }}</text>
					<text class="total-time">{{ totalTime }}</text>
				</view>
			</view>
			<view class="audio-play">
				<image :src="playMode === 'list' ? '/static/icons/music_list.png' : '/static/icons/refresh_one.png'"
					class="play-method" @click="togglePlayList" />
				<view class="play-button">
					<image src="/static/icons/arrow_left3.png" class="play-arrow" @click="playPrev" />
					<image :src="isPlaying ? '/static/icons/play_pause.png' : '/static/icons/play.png'"
						class="play-icon" @click="togglePlay" />
					<image src="/static/icons/arrow_right3.png" class="play-arrow" @click="playNext" />
				</view>
				<text class="play-speed-text" @click="togglePlaySpeed">{{ playSpeed }}倍</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		Howl,
		Howler
	} from 'howler'
	import http from '/api/index.js'
	import Tools from '/utils/index.js'

	export default {
		data() {
			return {
				pageTitle: '',
				id: '',
				audio: '',
				content: '',
				currentTime: '00:00',
				playSpeed: 1.0,
				sound: null,
				isPlaying: false,
				currentValue: 0,
				maxValueTime: 0,
				playMode: 'list', // 当前循环或者列表顺序播放
				updateInterval: null // 用于存储定时器的引用
			}
		},
		computed: {
			totalTime() {
				return Tools.removeMilliseconds(Tools.formatTime(this.maxValueTime)) || '00:00'
			},
			list() { // 播放列表
				return Tools.getStorage('CLASSIC_PLAYLIST')
			}
		},
		onLoad(options) {
			const {
				id,
				title
			} = options
			this.pageTitle = title
			this.id = id
			this.getDetail()
		},
		onUnload() {
			this.stopAudio()
		},
		methods: {
			initHowler() {
				console.error('初始化');
				this.sound = new Howl({
					src: [this.audio],
					html5: true, // 使用 HTML5 Audio
					onplay: this.handlePlay,
					onpause: this.handlePause,
					onend: this.handleAudioEnded,
					onseek: this.updateCurrentTimeAndScroll,
					onloaderror: function() {
					    console.error('音频加载失败');
					  },
					  onplayerror: function() {
					    console.error('播放失败');
					    sound.once('unlock', function() {
					      this.sound.play();
					    });
					  }
				})
				window.closeAudio = this.stopAudio;
			},
			handlePlay() {
				this.isPlaying = true
				this.maxValueTime = this.sound.duration();
				console.log('this.maxValueTime', this.maxValueTime)
				this.startUpdatingCurrentTime() // 开始更新播放进度
				uni.hideLoading()
			},
			handlePause() {
				this.isPlaying = false
				this.stopUpdatingCurrentTime() // 停止更新播放进度
			},
			handleAudioEnded() {
				this.isPlaying = false
				this.resetAudioState()
				this.stopUpdatingCurrentTime() // 停止更新播放进度
				this.playMode === 'list' ? this.playNextTrack() : this.restartAudio()
			},
			startUpdatingCurrentTime() {
				this.updateInterval = setInterval(() => {
					this.updateCurrentTimeAndScroll()
				}, 1000) // 每秒更新一次
			},
			stopUpdatingCurrentTime() {
				if (this.updateInterval) {
					clearInterval(this.updateInterval)
					this.updateInterval = null
				}
			},
			resetAudioState() {
				this.currentTime = '00:00'
				this.currentValue = 0
			},
			restartAudio() {
				this.sound.seek(0)
				this.sound.play()
			},
			playNextTrack() {
				const currentIndex = this.list.findIndex(item => item.id === this.id)
				const nextIndex = (currentIndex + 1) % this.list.length
				const nextItem = this.list[nextIndex]

				uni.redirectTo({
					url: `/pages/chineseClassics/detail?id=${nextItem.id}&title=${nextItem.name}`
				})
			},
			playPrev() {
				const currentIndex = this.list.findIndex(item => item.id === this.id)
				if (currentIndex === 0) {
					this.showToast('暂无上一首')
				} else {
					const prevItem = this.list[currentIndex - 1]
					this.navigateToTrack(prevItem)
				}
			},
			playNext() {
				const currentIndex = this.list.findIndex(item => item.id === this.id)
				if (currentIndex === this.list.length - 1) {
					this.showToast('暂无下一首')
				} else {
					this.playNextTrack()
				}
			},
			navigateToTrack(item) {
				uni.redirectTo({
					url: `/pages/chineseClassics/detail?id=${item.id}&title=${item.name}`
				})
			},
			showToast(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				})
			},
			togglePlayList() {
				this.playMode = this.playMode === 'current' ? 'list' : 'current'
			},
			updateCurrentTimeAndScroll() {
				if (!this.sound) {
					console.error('音频实例未初始化')
					return
				}

				const currentSeconds = this.sound.seek()
				this.currentTime = Tools.removeMilliseconds(Tools.formatTime(currentSeconds))
				this.currentValue = currentSeconds

				this.content.forEach((item, index) => {
					item.activeStatus = false
					const nextItem = this.content[index + 1]
					if (nextItem && Tools.convertToSeconds(item.time) <= currentSeconds && currentSeconds < Tools
						.convertToSeconds(nextItem.time)) {
						item.activeStatus = true
						this.scrollToActiveText(index)
					}
				})
			},
			scrollToActiveText(index) {
				this.$nextTick(() => {
					const element = this.$el.querySelector(`.text-content:nth-child(${index + 1})`)
					if (element) {
						element.scrollIntoView({
							behavior: 'smooth',
							block: 'center'
						})
					}
				})
			},
			handleBack() {
				uni.navigateBack({
					delta: 1
				})
			},
			getDetail() {
				uni.showLoading({
					title: '加载中'
				})
				const params = {
					userkey: uni.getStorageSync('userkey'),
					id: this.id
				}

				console.log("入参：：：", params)
				http.getGuoxueZhangjieDetail(params).then(res => {
					if (res.statusCode === 200) {
						console.log("出参：：：", res.data)
						const {
							audio,
							content
						} = res.data.data
						this.audio = audio
						this.content = content
						this.initHowler();
						setTimeout(() => {
							this.sound.play()
						}, 3000)
					}
				})
			},
			sliderChange(e) {
				if (!this.sound) return

				const currentSeconds = e.detail.value
				// 设置音频播放位置
				this.sound.seek(currentSeconds)
				// 更新当前时间显示
				this.currentTime = Tools.removeMilliseconds(Tools.formatTime(currentSeconds))
				this.currentValue = currentSeconds
			},
			togglePlaySpeed() {
				const speeds = [1.0, 1.5, 2.0, 0.5]
				const currentIndex = speeds.indexOf(this.playSpeed)
				const nextIndex = (currentIndex + 1) % speeds.length
				this.playSpeed = speeds[nextIndex]

				if (this.sound) {
					this.sound.rate(this.playSpeed)
					if (!this.sound.playing()) {
						this.sound.play()
					}
				} else {
					console.error('Howl 实例未初始化')
				}
			},
			togglePlay() {
				if (!this.sound) return
				this.isPlaying ? this.sound.pause() : this.sound.play()
			},
			stopAudio() {
				if (this.sound) {
					this.sound.stop()
				}
			},
			jumpToSentence(index) {
				if (!this.sound) {
					console.error('音频实例未初始化');
					return;
				}

				const targetTime = Tools.convertToSeconds(this.content[index].time);
				this.sound.seek(targetTime);
				this.sound.play();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		padding: 0 0 calc(env(safe-area-inset-bottom) + 30rpx);
		background-color: #f8f8f8;
	}

	.nav-header {
		position: sticky;
		top: 0;
		display: flex;
		align-items: center;
		padding: 20rpx 24rpx;
		background-color: #fff;
		z-index: 9999;

		.back-icon {
			display: flex;
			align-items: center;
			padding-right: 20rpx;
			cursor: pointer;
		}

		.back-image {
			width: 40rpx;
			height: 40rpx;
		}

		.page-title {
			flex: 1;
			max-width: 80%;
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
			text-align: center;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}

	.content {
		padding: 40rpx 24rpx 260rpx;

		.main-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
			text-align: center;
			margin-bottom: 20rpx;
		}

		.author {
			font-size: 28rpx;
			color: #999;
			text-align: center;
			margin-bottom: 40rpx;
		}

		.text-content {
			font-size: 32rpx;
			font-weight: 500;
			color: #000;
			text-align: center;
			line-height: 1.6;
			margin-bottom: 40rpx;

			&.active {
				color: #1DCE01;
			}
		}
	}

	.audio-controls {
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		left: 0;
		width: 100%;
		padding: 0 0 20rpx;
		background-color: #fff;

		.audio-times {
			padding: 30rpx 44rpx 8rpx;

			.audio-times-bottom {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 16rpx;

				.current-time,
				.total-time {
					font-size: 20rpx;
					color: #999;
				}
			}

			.progress-bar {
				.slider {
					margin: 0;
				}
			}
		}

		.audio-play {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 44rpx;

			.play-method {
				width: 40rpx;
				height: 40rpx;
			}

			.play-speed-text {
				min-width: 50rpx;
				font-size: 20rpx;
				font-weight: 500;
				color: #1DCE01;
				text-align: right;
			}

			.play-button {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-evenly;
			}

			.play-arrow {
				width: 60rpx;
				height: 60rpx;
			}

			.play-icon {
				width: 96rpx;
				height: 96rpx;
			}
		}
	}
</style>