<template>
	<view>
		<niceui-navbar class="my-navbar" :backShow="false" :content-height="23" :titleShow="false">
			<template #content>
				<view class="my-read-header">
					<view class="header-left" @click="goBack">
						<uni-icons type="back" color="#000" size="28"></uni-icons>
						<view class="header-title">
							<view class="oellipsis">{{ bookAllData.bookName || '' }}</view>
							<!-- <view class="title-publish">{{ bookInfo.bookInfo?.subName || '' }}</view> -->
						</view>
					</view>
					<view class="header-right">
						<image @click="showPageSet = true" class="right-btn set-btn" src="/static/images/readBook/setting.png"></image>
						<image @click="showLesson = true" class="right-btn" src="/static/images/readBook/lesson.png"></image>
					</view>
				</view>
			</template>
		</niceui-navbar>
		<view v-show="pageData.length" class="lesson-info between-vertical">
			<view>{{ currentLesson.title }}</view>
			<view>第{{ currentIndex + 1 }}/{{ pageData.length }}页</view>
		</view>

		<view class="read-book-con">
			<view class="uni-padding-wrap">
				<view class="page-section swiper">
					
					<canvas
						class="mycanvas"
						id="captureCanvas"
						canvas-id="captureCanvas"
						:style="{
							position: 'absolute',
							left: '-700px',
							width: bookImg.width + 'px',
							height: bookImg.height + 'px',
							visibility: 'hidden'
						}"
					></canvas>

					<view
						v-if="showMagnified && playSetData?.enlargeContent && currentItem.tempImgData"
						class="magnified-area"
						:style="{ left: currentItem.tempImgData.left, top: currentItem.tempImgData.top }"
					>
						<image :src="currentItem.tempImgData.tempImgPath" :style="{ width: currentItem.tempImgData.scaledWidth, height: currentItem.tempImgData.scaledHeight }" />
						<view  class="translate-con">
						<view  class="show-text" >
							<span v-html="currentItem.explainText"></span>
						</view>
						<view  class="translate">
							<view style="visibility: hidden;" >
								<span v-html="currentItem.explainText"></span>
							</view>
							<view class="translate-shadow">
								<span
									v-html="currentItem.explainText"
									:style="{ 'animation-duration': currentItem.duration / playSetData.playbackSpeed + 's' }"
									class="translate-text"
								></span>
							</view>
						</view>
						</view>
					</view>
					<view v-show="!playSetData?.enlargeContent && isPlay" class="display-chinese">{{ currentItem.explainText }}</view>
					<swiper
					    ref="myswiper"
						class="swiper"
						:disable-touch="(playMode == 3 && abPlayStep == 3) "
						@change="pageChange"
						:current="currentIndex"
						:autoplay="autoplay"
						:interval="interval"
						:duration="duration"
						easing-function="easeInOutCubic"
					>
						<swiper-item v-for="(item, index) in pageData" :key="index">
							<view v-if="currentIndex == index || currentIndex == index + 1 || currentIndex == index - 1" class="swiper-item">
								<image class="book-cover" :lazy-load="true" mode="widthFix" @load="getImgSize($event, item)" :src="item.pageUrl"></image>
								<view v-if="proportionW && item.frames.length > 0">
									<block v-for="(subitem, subindex) in item.frames" :key="subindex">
										<view
										    v-if="currentIndex == index"
											class="sentence-con"
											:class="{
												active: subitem.id == currentItem.id && isPlay && !playSetData?.enlargeContent,
												setBorder: playSetData.areaEnabled
											}"
											:data-index="index"
											:data-subindex="subindex"
											@click.stop="playSingleSentence($event, subitem)"
											:style="{
												width: (subitem.right - subitem.left) * proportionW + 'px',
												height: (subitem.bottom - subitem.top) * proportionH + 'px',
												left: subitem.left * proportionW + 'px',
												top: subitem.top * proportionH + 'px'
											}"
										></view>
									</block>
								</view>
							</view>
						</swiper-item>
					</swiper>
					
					<!-- ab复读 遮罩层 -->
					<view class="abPlay-shadow" v-if="playMode == 3 && abPlayStep < 3">
						<template v-for="(subitem, subindex) in  pageData[currentIndex].frames" :key="subindex">
							<view
								@click.stop="clickArea($event, subitem)"
								:data-index="currentIndex"
								:data-subindex="subindex"
								v-if="subitem.tempImgData"
								class="area-con animate__animated animate__bounceIn"
								:style="{
									left: subitem.left * proportionW + 'px',
									top: subitem.top * proportionH + 'px',
									'animation-delay': subindex * 0.05 + 's',
									'transform-style': 'preserve-3d',
									transform: 'translateZ(0)'
								}"
							>
								<image :src="subitem.tempImgData.tempImgPath" :style="{ width: subitem.tempImgData.width, height: subitem.tempImgData.height }" />
								<image
									v-if="abPlayPoint[0] && abPlayPoint[0].currentIndex == currentIndex && abPlayPoint[0].currentSubIndex == subindex"
									class="ab-img"
									src="/static/images/readBook/start.png"
								></image>
								<image
									v-else-if="abPlayPoint[1] && abPlayPoint[1].currentIndex == currentIndex && abPlayPoint[1].currentSubIndex == subindex"
									class="ab-img"
									src="/static/images/readBook/end.png"
								></image>
							</view>
						</template>
					</view>
					<!-- 测评 遮罩层 -->
					<view class="abPlay-shadow" v-if="(playMode == 4 || playMode == 5)">
						<template v-for="(subitem, subindex) in pageData[currentIndex].frames" :key="subindex">
							<view
								@click.stop="clickTestArea($event, subitem)"
								:data-index="currentIndex"
								:data-subindex="subindex"
								v-if="subitem.tempImgData"
								class="area-con animate__animated animate__bounceIn"
								:style="{
									left: subitem.left * proportionW + 'px',
									top: subitem.top * proportionH + 'px',
									'animation-delay': subindex * 0.05 + 's',
									'transform-style': 'preserve-3d',
									transform: 'translateZ(0)',
									border: subindex == currentSubIndex?'4px solid #4caf50': '',
									'z-index': subindex == currentSubIndex?99999: ''
								}"
							>    
								<image v-if="playMode == 4 ||testReadStep != 3" :src="subitem.tempImgData.tempImgPath" :style="{ width: subitem.tempImgData.width, height: subitem.tempImgData.height }" />
								<view v-else :src="subitem.tempImgData.tempImgPath" :style="{ width: subitem.tempImgData.width, height: subitem.tempImgData.height }" ></view>
							</view>
						</template>
					</view>
				</view>
			</view>
		</view>
		<!-- 测评 遮罩层 -->
		<view class="record-shadow" v-if="(playMode == 4 || playMode == 5) && (testReadStep == 2 || testReadStep == 3)">
			<view class="test-content-con">
				<view v-if="testReadStep == 2 || testReadStep == 3" class="test-read-sentence-con">
					<view>{{ testReadStep == 2 ? '播放原声' : playMode == 4 ? '请跟读' : '请背诵' }}</view>
					<view v-if="testReadStep == 2 || playMode == 4" class="test-read-sentence">
						<view class="test-sentence">
							<span>{{ currentItem.displayText }}</span>
						</view>
						<view class="test-sentence test-sentence-shadow">
							<span :style="{ 'animation-duration': currentItem.duration / playSetData.playbackSpeed + 's' }" class="translate-text">
								{{ currentItem.displayText }}
							</span>
						</view>
					</view>

					<view v-if="testReadStep == 3 && playMode == 5" class="test-read-sentence">
						<view  class="test-sentence">
							<span  
							 class="char"
							:style="{ color: item.score < 60 && item.dp_type == 0 ? 'red' : '' }"
							v-for="item in recordData">
							{{ item.char }}
							</span>
						</view>
					</view>
				</view>

				<view class="test-tips">{{ testReadStep == 2 ? '点击话筒立即开始' : '点击结束录音' }}</view>
				<view class="record-btn-con">
					<view class="progress-circle" v-if="countNum == 0" :style="{ 'animation-duration': recordNum + 's' }"></view>
					<image
						class="record-btn"
						@click.stop="handleRecordClick"
						:src="countNum ? '/static/images/readBook/record1.png' : '/static/images/readBook/record.png'"
					></image>
					<view class="count-num" v-if="countNum">{{ countNum }}</view>
				</view>
			</view>
			<view @click="quitTestRead" class="quit-test-read">
				<view>
					<i class="iconfont icon-exit-full"></i>
				</view>
				<view>退出</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<bookFootBtn class="book-foot-btn" ref="bookFootBtn" @isPlayAudio="isPlayAudio" @changePlayMode="changePlayMode" :textType="textType"></bookFootBtn>
		<!-- 添加 lesson 组件 -->
		<lesson class="lesson-con" @jumpPage="jumpPage" :lessons="bookInfo.lessons" :isVip="isVip"  v-if="showLesson" @close="showLesson = false" />
		<pageSet v-if="showPageSet" @close="closePageSet" />
		<evaluationResult
			@playTestAudio="playCurrentBs"
			@nextTestRead="nextTestRead"
			@retry="retry"
			@closeScoreMask="closeScoreMask"
			v-if="isEvaluating"
			:visible="isEvaluating"
			ref="scoreMask"
		></evaluationResult>
	</view>
</template>

<script>
import { nextTick } from 'vue';
import http from '/api/index.js';
import Tools from '/utils/index.js';
import lesson from './components/lesson.vue';
import pageSet from './components/pageSet.vue';
import bookFootBtn from './components/bookFootBtn.vue';
import evaluationResult from './components/evaluationResult.vue';

let windowWidth;
let windowHeight;
export default {
	components: {
		lesson,
		pageSet,
		bookFootBtn,
		evaluationResult
	},
	data() {
		return {
			indicatorDots: true,
			autoplay: false,
			interval: 2000,
			duration: 200,
			queryData: '',
			bookInfo: '',
			myAudio: uni.createInnerAudioContext(),
			currentItem: '',
			currentIndex: '',
			currentSubIndex: '',
			isPlay: false,
			isPlayEnd: false,
			playMode: 1, // 1单句 2连续 3ab复读 4评测 5 背诵
			nextSencten: '',
			proportionW: '',
			proportionH: '',
			bookImg: {
				width: '',
				height: ''
			},
			pageData: [],
			showMagnified: false,
			showLesson: false,
			showPageSet: false,
			currentLesson: {},
			playSetData: Tools.getStorage('playSetData'),
			abPlayStep: 3,
			abPlayPoint: [], // {currentIndex: 1, currentSubIndex: ''}
			playTimer: '',
			testReadStep: 1, // 显示遮罩和句子  2 播放句子 3 句子播完进入跟读
			countNum: 0,
			countNumDownTimer: '',
			recordNum: 0,
			recorderManager: null,
			recordFile: '', // 存储录音文件路径
			evaluationResult: null, // 评测结果
			isEvaluating: false, // 是否正在评测
			testResult: {},
			isVip: false,
			bookAllData: '',
			recordTimer: '',
			recordData: '',
			textType: 1
		};
	},
	watch: {
		currentIndex() {
			let num = this.currentIndex + this.bookInfo.lessons[0].beginPage;
			for (let i = 0; i < this.bookInfo.lessons?.length; i++) {
				if (num > this.bookInfo.lessons[this.bookInfo.lessons.length - 1].beginPage) {
					this.currentLesson = this.bookInfo.lessons[this.bookInfo.lessons.length - 1];
					break;
				}
				if (this.bookInfo.lessons[i].beginPage < num && this.bookInfo.lessons[i + 1].beginPage > num) {
					this.currentLesson = this.bookInfo.lessons[i];
					break;
				}
			}
		},
		isPlay() {
			if (this.isPlay) {
				this.setAudioInfo(this.currentItem);
			} else {
				this.myAudio && this.myAudio.pause();
			}
		}
	},
	methods: {
		async init() {
			// Convert setTimeout to Promise
			await new Promise((resolve) => {
				// setTimeout(() => {
				uni.getSystemInfo({
					success: function (res) {
						windowWidth = res.windowWidth;
						windowHeight = res.windowHeight;
						resolve();
					}
				});
				// }, 500);
			});
			this.initPlayData();
			await this.getBookDetail();
			this.audioInit();
		},
		getBookDetail() {
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				this.bookAllData = JSON.parse(callNative.getReadBookAllData());
				// const data = JSON.parse(callNative.getBookJson());
				// this.handBookData(data?.lists)
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				// const data = window.webkit.messageHandlers.getReadAllData.postMessage();
				this.bookAllData = callNative.getReadBookAllData();
				// this.handBookData(data?.lists)
			}
			// this.bookAllData = {"bookId": "1592", "bookName": "一年级上英语（人教PEP版）", "isBuy": "0", "jsonFilePath": "/storage/emulated/0/Android/data/com.zhouhan.ai/files/Download/read/.4_1592/data.json", "readBookRoute": "/storage/emulated/0/Android/data/com.zhouhan.ai/files/Download/read/.4_1592"}
			console.log('userkey', this.queryData, this.bookAllData);
			// this.queryData.bookId = 1592;
			http.getBookDetail({ userkey: this.queryData.userKey, book_id: this.bookAllData.bookId }).then((res) => {
				console.log(res.data.data);
				const data = res.data.data;
				this.handBookData(data?.lists);
				if(data?.book_name && data?.book_name.includes('语文')) {
					this.textType = 2;
				}
			});
		},
		handBookData(data) {
			const lessons = [];
			const tmpPages = [];
			const pages = [];
			let num = 1;
			data.forEach((item) => {
				lessons.push({
					id: item.id,
					title: item.desc,
					unit: item.name,
					beginPage: num
				});
				item.images.map((subItem) => {
					subItem.pageNo = num;
					tmpPages.push(subItem);
					num += 1;
				});
			});
			tmpPages.forEach((item) => {
				let frames = [];
				item.audios.forEach((subItem) => {
					const point = subItem.zuobiao.split(',');
					frames.push({
						displayText: subItem.english,
						explainText: subItem.chinese,
						left: point[0] / item.width,
						top: point[1] / item.height,
						right: point[2] / item.width,
						bottom: point[3] / item.height,
						// mp3url: this.bookAllData.readBookRoute + +'/' + item.mp3url,
						mp3url: subItem.audio,
						pageNo: item.pageNo,
						id: subItem.id
					});
				});
				pages.push({
					pageNo: item.pageNo,
					// pageUrl:'content://' + this.bookAllData.readBookRoute +'/' + item.image_url,
					pageUrl: item.image_url,
					id: item.id,
					frames
				});
			});
			this.initBookDetail({
				lessons,
				pages
			});
		},
		initBookDetail(data) {
			uni.setNavigationBarTitle({
				title: this.bookAllData.bookName
			});

			this.pageData = data.pages;
			this.bookInfo = data;
			this.currentSubIndex = 0;
			this.currentIndex = Tools.getStorage('readBookBeginPage-' + this.bookAllData.bookId) || 0;
			this.currentItem = data.pages[this.currentIndex].frames.length > 0 ? data.pages[this.currentIndex].frames[0] : '';
			this.currentLesson = data.lessons[0];
			
		},
		pageChange(data) {
			if (data.detail.current >= this.bookInfo.lessons[2].beginPage && !this.isVip) {
				this.currentIndex = this.currentIndex - 1;
				Tools.openVip();
				return;
			}
			this.showMagnified = false;
			this.isPlay = false;
			let currentPageData = this.bookInfo.pages[data.detail.current];
			currentPageData.frames && currentPageData.frames.length > 0 && (this.currentItem = currentPageData.frames[0]);
			this.currentIndex = data.detail.current;
			this.currentSubIndex = 0;

			if (this.playMode == 3 && this.abPlayStep == 3 && this.currentIndex == this.abPlayPoint[0].currentIndex) {
				//ab复读
				this.currentSubIndex = this.abPlayPoint[0].currentSubIndex;
				this.currentItem = currentPageData.frames[this.currentSubIndex];
			}
			if ((this.playMode == 2 || this.playMode == 3) && this.abPlayStep > 2) {
				this.playAudio();
				this.$refs.bookFootBtn.changePlayFlag(true);
			}
			if ((this.playMode == 3 || this.playMode == 4) && this.abPlayStep < 3) {
				this.$nextTick(() => {
					this.getCurrentPageAllAreas();
				});
			}
		},
		animationfinish() {
			this.$nextTick(() => {
				// this.getCurrentPageAllAreas();
			});
		},
		jumpPage(page) {
			this.currentIndex = Number(page) - Number(this.bookInfo.lessons[0].beginPage);
			this.showLesson = false;
		},
		getImgSize(event, item) {
			if (this.proportionW) {
				return false;
			}
			this.bookImg = {
				width: event.detail.width,
				height: event.detail.height
			};
			// this.proportionW = event.detail.width/windowWidth;
			// this.proportionH  = event.detail.height * this.proportionW;
			this.proportionW = windowWidth;
			this.proportionH = event.detail.height / (event.detail.width / windowWidth);
			this.$nextTick(() => {
				this.getCurrentPageAllAreas();
			});
		},
		audioInit() {
			this.audioTimeUpdate();
			this.myAudio.onWaiting((res) => {
				this.myAudio.play();
			});
			this.closePageSet();
		},
		closePageSet() {
			this.showPageSet = false;
			this.playSetData = Tools.getStorage('playSetData');
			this.myAudio.playbackRate = this.playSetData.playbackSpeed;
			this.myAudio._audio.defaultPlaybackRate = this.playSetData.playbackSpeed; // 在音频加载完成后设置播放速率
		},
		resetAnimate() {
			// 重置 translate-text 动画
			this.$nextTick(() => {
				const translateText = this.$el.querySelector('.translate');

				if (translateText) {
					translateText.style.display = 'none'; // 停止当前动画
					translateText.offsetHeight; // 触发重绘
					setTimeout(() => {
						translateText.style.display = 'inline'; // 停止当前动画
						// translateText.style.animation = `erase ${this.currentItem.duration}s linear forwards`; // 重新应用动画
						// console.log(translateText.style.animation, 'translateText.style.animation')
					}, 200);
				}
			});
		},
		// 新增截图方法
		async captureSelectedArea(item) {
			if (item['tempImgData']) {
				return;
			}
			// 计算截图区域尺寸
			const sentenceWidth = (item.right - item.left) * this.bookImg.width;
			const sentenceHeight = (item.bottom - item.top) * this.bookImg.height;
			const sentenceLeft = item.left * this.bookImg.width;
			const sentenceTop = item.top * this.bookImg.height;

			// 获取屏幕尺寸
			const systemInfo = uni.getSystemInfoSync();
			const screenWidth = systemInfo.windowWidth;
			const screenHeight = systemInfo.windowHeight;

			// 计算放大区域的尺寸和位置
			let width, height, left, top, scale;
			if (1 / (item.right - item.left) > 1.5) {
				// 窄文本放大1.5倍
				width = (item.right - item.left) * this.proportionW;
				height = (item.bottom - item.top) * this.proportionH;
				scale = 1.5;
			} else {
				// 宽文本适应屏幕宽度
				width = this.proportionW;
				height = ((item.bottom - item.top) * this.proportionH) / (item.right - item.left);
				scale = 1;
			}

			// 计算放大后的尺寸
			const scaledWidth = width * scale;
			const scaledHeight = height * scale;

			// 初始位置
			left = item.left * this.proportionW;
			top = item.top * this.proportionH + 110;

			// 确保不超出屏幕边界
			if (left + scaledWidth > screenWidth) {
				left = screenWidth - scaledWidth - 20;
			}
			if (left < 0) {
				left = 0;
			}

			if (top + scaledHeight > screenHeight) {
				top = screenHeight - scaledHeight - 20; // 留出底部间距
			}
			if (top < 110) {
				// 保持顶部最小间距
				top = 110;
			}

			let magnifiedStyle = {
				width: width + 'px',
				height: height + 'px',
				scaledWidth: scaledWidth + 'px',
				scaledHeight: scaledHeight + 'px',
				left: left + 'px',
				top: top + 'px',
				transform: `scale(${scale})`,
				transformOrigin: 'left top',
				num: Math.random() * 1000
			};
			const canvasId = 'captureCanvas';
			const ctx = uni.createCanvasContext(canvasId, this);
			ctx.clearRect(0, 0, sentenceWidth, sentenceHeight);
			ctx.drawImage(this.pageData[this.currentIndex].pageUrl, sentenceLeft, sentenceTop, sentenceWidth, sentenceHeight, 0, 0, sentenceWidth, sentenceHeight);
			await new Promise((resolve) => {
				ctx.draw(false, () => {
					this.$nextTick(() => {
						uni.canvasToTempFilePath(
							{
								canvasId,
								x: 0,
								y: 0,
								width: sentenceWidth,
								height: sentenceHeight,
								destWidth: sentenceWidth,
								destHeight: sentenceHeight,
								success: (res) => {
									// console.log('success', res);
									item['tempImgData'] = Object.assign(magnifiedStyle, { tempImgPath: res.tempFilePath });
									resolve();
								},
								fail: (err) => {
									console.log('err', err);
								}
							},
							this
						);
					});
				});
			});
		},
		async getCurrentPageAllAreas(flag = false) {
			setTimeout(async () =>{
				let i = 0;
				let currentPageFrames = this.pageData[this.currentIndex].frames || [];
				do {
					await this.captureSelectedArea(currentPageFrames[i]);
					i++;
					// if(flag) {
					// setTimeout(()=>{
					// 	console.log(i)
					// 	Tools.audioOpenPopup();
					// },i*200)
					// }
				} while (i < currentPageFrames.length);
			},800)
			
		},
		async getAllAreas() {
			let i = 0;
			do {
				await this.getCurrentPageAllAreas(i);
				i++;
				if (i == this.pageData.length) {
					Object.freeze(this.bookInfo);
					Object.freeze(this.pageData);
				}
			} while (i < this.pageData.length);
		},
		playSingleSentence(event, item) {
			if (this.playMode == 3 && this.abPlayStep == 3) {
				return;
			}

			const data = event.target.dataset;
			this.currentItem = item;

			// 处理音频播放
			if (this.isPlay) {
				this.isPlay = false;
				this.playAudio();
			} else {
				this.isPlay = true;
			}
			this.currentIndex = Number(data.index);
			this.currentSubIndex = Number(data.subindex);
		},
		clickArea(event, item) {
			console.log(event, 'event');
			const data = event.currentTarget.dataset;
			console.log(data);
			if (this.playMode == 3 && this.abPlayStep == 1) {
				this.abPlayPoint = [
					{
						currentIndex: Number(data.index),
						currentSubIndex: Number(data.subindex),
						value: Number(data.index) + String(data.subindex)
					}
				];
				this.abPlayStep += 1;
				this.$refs.bookFootBtn.changeAbPlayStep(this.abPlayStep);
			} else if (this.playMode == 3 && this.abPlayStep == 2) {
				this.$refs.bookFootBtn.changeAbPlayStep(this.abPlayStep);
				this.abPlayPoint.push({
					currentIndex: Number(data.index),
					currentSubIndex: Number(data.subindex),
					value: Number(data.index) + String(data.subindex)
				});
				this.abPlayPoint.sort((last, next) => {
					return last.value - next.value;
				});
				this.currentIndex = this.abPlayPoint[0].currentIndex;
				this.$nextTick(() => {
					// ab复读同一个页面
					this.currentIndex = this.abPlayPoint[0].currentIndex;
					this.currentSubIndex = this.abPlayPoint[0].currentSubIndex;
					this.currentItem = this.pageData[this.currentIndex].frames[this.currentSubIndex];
					this.isPlay = true;
					this.abPlayStep += 1;
					this.$refs.bookFootBtn.changeAbPlayStep(this.abPlayStep);
				});
			}
		},
		async clickTestArea(event, item) {
			const data = event.currentTarget.dataset;
			this.currentIndex = Number(data.index);
			this.currentSubIndex = Number(data.subindex);
			this.currentItem = this.pageData[this.currentIndex].frames[this.currentSubIndex];
			this.testReadStep = 2;
			await this.setAudioInfo();
			this.setCountNumDown(Math.floor(this.currentItem.duration) + 1);
			this.isEvaluating = false;
			this.stopPlayCurrentBs();
		},

		setCountNumDown(num) {
			console.log(num, 'num');
			this.countNum = num;
			this.recordNum = num + 3;
			this.recordData = [];
			clearInterval(this.countNumDownTimer);
			this.countNumDownTimer = setInterval(() => {
				this.countNum -= 1;
				if (this.countNum <= 0) {
					clearInterval(this.countNumDownTimer);
					this.startRecording(); // Stop recording when countdown reaches 0
				}
			}, 1000);
		},
		playAudio() {
			setTimeout(() => {
				this.isPlay = true;
			});
		},
	
	  async	setAudioInfo(item = this.currentItem) {
			if (this.playSetData?.enlargeContent && this.playMode != 4 && this.playMode != 5) {
				this.resetAnimate();
				if (!item['tempImgData']) {
					this.$nextTick(() => {
						this.captureSelectedArea(item);
						setTimeout(() => {
							this.getCurrentPageAllAreas();
						}, 1000);
					});
				}
				// 处理截图
				// this.$nextTick(() => {
				// 	// this.getCurrentPageAllAreas();
				// 	this.captureSelectedArea(item);
				// });
				// 音频播放结束后隐藏放大区域
				this.showMagnified = true;
			}

			this.isPlayEnd = false;
			this.myAudio.src = item.mp3url;
			this.myAudio.autoplay = true;
			this.myAudio.play();
			await   new Promise((resolve, reject) => {
				this.myAudio.onCanplay(() => {
					this.currentItem.duration = this.myAudio.duration;
					resolve();
				});
			});

			if (item.pageNo != this.currentIndex + 1 && this.playMode > 1) {
				this.currentIndex = Number(item.pageNo) - 1;
			}
		},

		audioTimeUpdate() {
			// #ifdef H5

			this.myAudio.onEnded(() => {
				this.showMagnified = false;
				this.isPlayEnd = true;
				if (this.playMode == 4 || this.playMode == 5) {
					this.testReadStep = 3;
				}
				this.audioIsPause();
				// this.isPlay = false;
			});
			// #endif
		},
		audioIsPause() {
			this.showMagnified = false;
			if (this.playMode == 1) {
				this.isPlay = false;
			}
			if (this.playMode == 2) {
				this.currentItem = this.setNextSentence(this.currentIndex, this.currentSubIndex);
				this.setAudioInfo(this.currentItem);
			}
			if (this.playMode == 3 && this.currentIndex + String(this.currentSubIndex) < this.abPlayPoint[1].value) {
				this.currentItem = this.setNextSentence(this.currentIndex, this.currentSubIndex);
				this.setAudioInfo(this.currentItem);
			} else if (this.playMode == 3 && this.currentIndex + String(this.currentSubIndex) == this.abPlayPoint[1].value) {
				this.currentIndex = this.abPlayPoint[0].currentIndex;
				this.currentSubIndex = this.abPlayPoint[0].currentSubIndex;
				this.currentItem = this.pageData[this.currentIndex].frames[this.currentSubIndex];
				this.setAudioInfo(this.currentItem);
			}
		},
		isPlayAudio(flag) {
			if (!this.currentItem) {
				uni.showToast({
					title: '当前页没有点读',
					icon: 'none',
					duration: 3000
				});
				return;
			}
			// this.playMode = 2;
			this.isPlay = flag;
			if (!flag) {
				this.myAudio.pause();
				this.showMagnified = false;
				clearTimeout(this.playTimer);
				this.stopPlayCurrentBs();
				clearInterval(this.countNumDownTimer);
				clearTimeout(this.recordTimer);
			}
		},
		changePlayMode(num = 1, flag = true, abPlayStep = 3, testReadStep = 1) {
			this.playMode = num;
			this.isPlay = flag;
			this.abPlayStep = abPlayStep;
			this.showMagnified = false;
			this.testReadStep = testReadStep;
			if (this.abPlayStep == 1) {
				this.abPlayPoint = [];
			}
			if (this.playMode > 2) {
				this.getCurrentPageAllAreas(true);
			}
		},
		closeScoreMask() {
			this.testReadStep = 1;
			this.isEvaluating = false;
			this.stopPlayCurrentBs();
			this.$refs.bookFootBtn.changeTestReadStep(this.testReadStep);
		},
		quitTestRead() {
			this.$refs.bookFootBtn.goBack();
		},
		setNextSentence(index, subindex) {
			if (this.bookInfo.pages[index].frames && this.bookInfo.pages[index].frames.length - 1 > subindex) {
				this.nextSencten = this.bookInfo.pages[index].frames[subindex + 1];
				this.currentSubIndex = subindex + 1;
			} else if (this.bookInfo.pages.length - 1 > index + 1 && this.bookInfo.pages[index + 1].frames && this.bookInfo.pages[index + 1].frames.length > 0) {
				this.nextSencten = this.bookInfo.pages[index + 1].frames[0];
				this.currentSubIndex = 0;
				this.currentIndex = index + 1;
			} else if (this.bookInfo.pages.length - 1 > index + 1) {
				this.setNextSentence(index + 2, 0);
				this.currentSubIndex = 0;
				this.currentIndex = index + 2;
			}
			return this.nextSencten;
		},
		goBack() {
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				console.log('执行app方法');
				callNative.finishCurrentPage();
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.finishCurrentPage.postMessage(null);
			}
		},
		openPopup() {
			this.$refs.popup.open();
		},
		beforeLeavePage() {
			this.isPlay = false;
			// this.myAudio.pause();
			this.playMode = 1;
			clearInterval(this.countNumDownTimer);
			clearTimeout(this.recordTimer);
			clearInterval(this.playTimer);
			this.stopPlayCurrentBs();
			Tools.setStorage('readBookBeginPage-' + this.bookAllData.bookId, this.currentIndex);
		},
		initPlayData() {
			if (!this.playSetData) {
				this.playSetData = {
					playbackSpeed: 1,
					timeInterval: 4,
					readingCount: 1,
					translationEnabled: false,
					markEnabled: true,
					areaEnabled: true,
					enlargeContent: true,
					longSentenceBreak: false,
					playOriginalSound: true
				};
				Tools.setStorage('playSetData', this.playSetData);
			}
		},

		handleRecordClick() {
			if (this.testReadStep == 2) {
				this.myAudio.pause();
				clearInterval(this.countNumDownTimer);
				this.countNum = 0;
				this.startRecording();
			} else if (this.testReadStep == 3) {
				this.stopRecording();
			}
		},
		retry() {
			this.isEvaluating = false;
			this.stopPlayCurrentBs();
			this.setAudioInfo();
			this.testReadStep = 2;
			this.setCountNumDown(Math.floor(this.currentItem.duration) + 1);
			this.$refs.bookFootBtn.changeTestReadStep(this.testReadStep);
		},
		async nextTestRead() {
			this.isEvaluating = false;
			this.stopPlayCurrentBs();
			this.getNextItem(this.currentIndex, this.currentSubIndex);
			// this.currentSubIndex += 1;
			// if (this.currentSubIndex == this.pageData[this.currentIndex].frames.length) {
			// 	this.currentIndex += 1;
			// }
			// this.currentItem = this.pageData[this.currentIndex].frames[this.currentSubIndex];
			await this.setAudioInfo();
			this.testReadStep = 2;
			this.setCountNumDown(Math.floor(this.currentItem.duration) + 1);
			this.$refs.bookFootBtn.changeTestReadStep(this.testReadStep);
		},
		getNextItem(currentIndex, currentSubIndex) {
			currentSubIndex += 1;
			if (currentSubIndex == this.pageData[currentIndex].frames.length) {
				currentIndex += 1;
				if(currentIndex == this.pageData.length) {
					Tools.toast('没有更多的句子了。。。')
					return;
				}
				if(currentIndex >= this.bookInfo.lessons[2].beginPage && !this.isVip) {
					Tools.openVip(); // 非会员
					return;
				}
				if(!this.pageData[currentIndex].frames[0]) {
					this.getNextItem(currentIndex, -1); // 空白页 没有点读
					return;
				}	
			}
			this.currentIndex = currentIndex;
			this.currentSubIndex = currentSubIndex;
			this.currentItem = this.pageData[this.currentIndex].frames[this.currentSubIndex];
		},
		//  调js告知实时评测数据
		getRecordData(res) {
			console.log(res, 'getRecordData')
			this.recordData = res;
		},
		//  Android背诵完成调js告知总分数等数据
		overRecord(res) {
			const data = {
				suggestedScore: res,
				pronAccuracy: 0,
				pronFluency: 0,
				pronCompletion: 0
			};
			this.$nextTick(() => {
				this.$refs.scoreMask.getResult(data);
			});

			console.log('overRecord方法执行', res);
		},
		startRecording() {
			this.recordData = [];
			this.testReadStep = 3; // 更新状态为录音中
			window.getRecordData = this.getRecordData;
			window.overRecord = this.overRecord;
			let data = this.currentItem.displayText;
			if(this.textType == 2) {
				data = this.currentItem.explainText;
			}
			console.log(data, this.textType);
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				callNative.startBs(data, this.textType);
				console.log('startBs方法已经执行');
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.startBs.postMessage(data, this.textType);
			}
			clearTimeout(this.recordTimer);
			this.recordTimer = setTimeout(() => {
				this.stopRecording();
			}, this.recordNum * 1000);
		},
		playCurrentBs() {
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				callNative.playCurrentBs();
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.playCurrentBs.postMessage();
			}
		},
		stopPlayCurrentBs() {
			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				callNative.stopPlayCurrentBs();
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.stopPlayCurrentBs.postMessage();
			}
		},
		stopRecording() {
			clearTimeout(this.recordTimer);
			// 更新状态
			// this.testReadStep = 3;
			// this.startSpeechEvaluation(res.tempFilePath);
			this.isEvaluating = true;
			this.testReadStep = 4;

			if (/(Android)/i.test(navigator.userAgent)) {
				//判断Android
				callNative.bsCompletedAndResult();
				console.log('bsCompletedAndResult方法已经执行');
			} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				//判断iPhone|iPad|iPod|iOS
				window.webkit.messageHandlers.bsCompletedAndResult.postMessage();
			}

			// 显示录音结束状态
			uni.showToast({
				title: '录音结束',
				icon: 'none'
			});
		}
	},
	onLoad(option) {
		this.queryData = option;
		window.closeAudio = this.beforeLeavePage;
		this.init();
	},
	onShow() {
		this.bookAllData = JSON.parse(callNative.getReadBookAllData());
		this.isVip = Number(this.bookAllData.isBuy);
		// this.isVip = 0;
	},
	onHide() {
		this.beforeLeavePage();
	},
	unmounted() {
		this.beforeLeavePage();
	},
	onBackPress() {
		this.beforeLeavePage();
	},
	onShareAppMessage(res) {
		if (res.from === 'button') {
			// 来自页面内分享按钮
			console.log(res.target);
		}
		return {
			title: '教材点读机,帮助小学生趣味学英语,提升英语成绩必备神器'
			// imgUrl:
		};
	},
	mounted() {}
};
</script>
<style lang="less" scoped>
.my-read-header {
	width: 100%;
	box-sizing: border-box;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15upx 30upx;
	background-color: transparent;

	.header-left {
		display: flex;
		align-items: center;
	}

	.header-title {
		font-weight: 500;
		font-size: 28rpx;
		color: #333333;
		max-width: 470upx;

		.title-publish {
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;
			margin-top: 10upx;
		}
	}

	.header-right {
		.right-btn {
			width: 60upx;
			height: 60upx;
		}
		.set-btn {
			margin-right: 30upx;
		}
	}
}
.lesson-info {
	width: 100%;
	height: 64upx;
	line-height: 64upx;
	background: #f5f5f5;
	font-size: 24upx;
	color: #999999;
	padding: 0 30upx;
	box-sizing: border-box;
}
.read-book-con {
	width: 100%;
	height: calc(100vh - 88rpx - 64rpx - 180rpx - 60rpx); /* Navbar (88rpx) + lesson-info (64rpx) + footer (180rpx) */
	max-width: 1024px;
	margin: 0 auto;
	position: relative;
	box-sizing: border-box;
	overflow: hidden;
	// overflow-y: auto;
	// padding-bottom: 30px;

	.uni-padding-wrap {
		width: 100vw;
		height: 100%;

		.swiper {
			width: 100vw;
			height: 100%;

			.swiper-item {
				width: 100vw;
				height: 100%;
				display: inline-block;
				overflow: hidden;
				overflow-y: auto !important;
				display: inline-block;
				position: relative;

				.book-cover {
					width: 100%;
					height: 100%;
					// height: 100vh;200px
				}

				.sentence-con {
					position: absolute;
					z-index: 99;
				}
				.setBorder {
					border: 2rpx solid #3ac8b0;
				}

				.active {
					background: rgba(237, 145, 38, 0.2);
					// border-radius: 12upx;
					border: 2px solid #3ac8b0;
				}
			}
			.abPlay-shadow {
				width: 100%;
				height: calc(100vh - 10px);
				position: absolute;
				top: 0;
				left: 0;
				z-index: 99;
				background-color: rgba(0, 0, 0, 0.6);
				 pointer-events: none; /* 设置为none，让鼠标事件穿透 */
				.area-con {
					position: absolute;
					z-index: 100;
					transition: all 0.5s ease;
					background-color: #fff;
					font-size: 0;
					pointer-events: auto; /* 确保按钮可以点击 */
					.ab-img {
						width: 20px;
						height: 20px;
						position: absolute;
						left: -5px;
						top: -20px;
					}
				}
			}
		}
	}
	.display-chinese {
		background-color: rgba(50, 50, 50, 0.85);
		color: #fff;
		position: absolute;
		bottom: 50upx;
		left: 50%;
		transform: translateX(-50%);
		width: 70%;
		line-height: 32px;
		text-align: center;
		border-radius: 10px;
		z-index: 99;
	}

	.go-back {
		position: absolute;
		left: 30upx;
		top: 10upx;
		z-index: 9999999;
		color: #000;
		font-size: 60upx;
		font-weight: bold;
		width: 30rpx;
		height: 30rpx;
	}
}
.iconfanhui1 {
	width: 30rpx;
	height: 30rpx;
	background-color: red;
	font-size: 100rpx;
}

.magnified-area {
	position: fixed;
	z-index: 1000;
	background: #fff;
	box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.3);
	transform-origin: left center;
	transition: all 0.6s ease;
	border: 2px solid #3ac8b0;
	border-radius: 10px;
	max-width: 100vw;
	max-height: calc(100vh - 130px); // 预留顶部和底部空间
	// box-sizing: border-box;
	overflow: hidden; // 防止内容溢出
	padding: 2upx 10upx 10upx 2upx;
}
.translate-con {
	position: relative;
	display: block;
	// padding: 20upx 0; 
}
.show-text {
	line-height: 36upx;
}
.translate {
	display: block;
	line-height: 36upx;
	line-height: 36upx;
	position: absolute;
	left: 0;
	top: 0;
	max-width: 100vw;
	overflow: hidden; // 防止内容溢出
	color: #000;
}
.translate-shadow {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 999;
	width: 100%;
}

@property --p {
	syntax: '<percentage>';
	inherits: false;
	initial-value: 0%;
}
.translate-text {
	--p: 0%;
	background: linear-gradient(to right, transparent var(--p), #999 var(--p));
	-webkit-background-clip: text;
	background-clip: text;
	color: transparent;
	inset: 0;
	line-height: 36upx;
	font-weight: 400;
	animation: erase 2s linear forwards;
}

@keyframes erase {
	0% {
		--p: 0%;
	}
	100% {
		--p: 100%;
	}
}

.record-shadow {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 99999999;
	width: 100%;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.6);
	.test-content-con {
		position: absolute;
		bottom: 40upx;
		left: 50%;
		transform: translateX(-50%);
		text-align: center;
		box-sizing: border-box;
	}
	.test-read-sentence-con {
		background-color: #fff;
		border: 2rpx solid #79cb01;
		border-radius: 12rpx;
		min-width: 300rpx;
		padding: 10px;
		box-sizing: border-box;
	}
	.test-read-sentence {
		position: relative;
		max-width: 90vw;
		// background-color: #fff;
		// border: 2rpx solid #79cb01;
		font-size: 30rpx;
		margin: 0 auto;

		.test-sentence {
			width: 100%;
			text-align: center;
			font-size: 30rpx;
			padding: 10px;
		}
		.test-sentence-shadow {
			position: absolute;
			left: 0;
			top: 0;
			text-align: center;
			font-size: 30rpx;
		}
	}
	.test-tips {
		width: 100%;
		margin-top: 10px;
		text-align: center;
		color: #fff;
	}
	.record-btn-con {
		width: 135upx;
		height: 135upx;
		border-radius: 50%;
		overflow: hidden;
		position: relative;
		margin: 20px auto 0;
		// background-color: rgba(0, 0, 0, 0.6);
		// 新增圆形进度条样式
		.progress-circle {
			--p: 100%;
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			border-radius: 50%;
			background: conic-gradient(#ddd var(--p), #4caf50 0);
			animation: erase 2s linear forwards;
			pointer-events: none;
		}

		.record-btn {
			width: 120upx;
			height: 120upx;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			z-index: 2; /* 确保按钮在最上层 */
			pointer-events: auto; /* 确保按钮可以点击 */
		}

		.count-num {
			width: 100%;
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			color: #fff;
			background-color: rgba(0, 0, 0, 0.6);
			font-size: 60upx;
			text-align: center;
			line-height: 120upx;
			z-index: 2;
			pointer-events: none; /* 确保按钮可以点击 */
		}
	}
	.quit-test-read {
		display: flex;
		justify-content: center;
		text-align: center;
		flex-direction: column;
		position: absolute;
		right: 40px;
		bottom: 40px;
		color: #fff;
	}
}
.char {
	margin-right: 3px;
}
.mycanvas {
	transform: translateZ(0);
	transform-style: preserve-3d; /* 保留3D空间 */
}
</style>
