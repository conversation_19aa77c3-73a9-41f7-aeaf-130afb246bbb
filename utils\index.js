// 引入crypto-js库
import CryptoJS from 'crypto-js';

// 设置密钥和初始向量
const KEY = 'erty0cade22611ec9aaf5254ea5488u9'; // 密钥
const IV = 'erty0cade22611ec'; // 初始向量，根据需要设置
// const KEY = '79990cade22611ec9aaf5254ea5493e6'; // 正式环境密钥
// const IV = '79990cade22611ec'; // 初始向量，根据需要设置s

// AES加密
function encrypt(data) {
	const key = CryptoJS.enc.Utf8.parse(KEY);
	const iv = CryptoJS.enc.Utf8.parse(IV);
	const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(data), key, {
		iv: iv,
		mode: CryptoJS.mode.CBC,
		padding: CryptoJS.pad.Pkcs7
	});
	return encrypted.toString(); // 返回Base64编码的字符串
}

// AES解密
function decrypt(encryptedData) {
	const key = CryptoJS.enc.Utf8.parse(KEY);
	const iv = CryptoJS.enc.Utf8.parse(IV);
	const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
		iv: iv,
		mode: CryptoJS.mode.CBC,
		padding: CryptoJS.pad.Pkcs7
	});
	return decrypted.toString(CryptoJS.enc.Utf8); // 返回解密后的字符串
}

function base64Decode(str) {
	return CryptoJS.enc.Base64.parse(str);
}

function md5Encrypt(data) {
	return CryptoJS.MD5(data).toString();
}

function throttle(func, delay) {
	let startTime = Date.now() - delay
	return function() {
		let curTime = Date.now()
		let remaining = delay - (curTime - startTime)
		let context = this
		let args = arguments
		if (remaining <= 0) {
			func.apply(context, args)
			startTime = Date.now()
		} else {
			uni.showToast({
				title: '请勿频繁点击',
				icon: 'none'
			})
		}
	}
}

export function toast(message) {
	uni.showToast({
		title: message,
		icon: 'none',
		duration: 3000,
	})
}

function goPage(type, url) {
	switch (type) {
		case 1: //保留当前页面，跳转到应用内的某个页面
			uni.navigateTo({
				url,
			});
			break;
		case 2: //关闭当前页面，跳转到应用内的某个页面
			uni.redirectTo({
				url,
			});
			break;
		case 3: //关闭所有页面，打开到应用内的某个页面
			uni.reLaunch({
				url,
			});
			break;
	}
}

function formatRichText(html) {
	let newContent = html.replace(/\<img/gi, '<img style="max-width:100%;height:auto;display:block;"');
	return newContent;
}

function getStorage(key) {
	const storage = uni.getStorageSync(key)

	if (!storage) return null

	const {
		data,
		expiration
	} = storage
	const timestamp = Date.now()

	if (expiration && timestamp > expiration) {
		removeStorage(key)
		return null
	} else {
		return data
	}
}

function setStorage(key, value, time) {
	let storage = {
		data: value
	}
	if (time) {
		const timestamp = Date.now()
		const expiration = timestamp + time
		storage.expiration = expiration
	}

	uni.setStorageSync(key, storage)
}

function removeStorage(key) {
	uni.removeStorageSync(key)
}

function base64src(base64data) {
	const fsm = wx.getFileSystemManager();
	const FILE_BASE_NAME = 'tmp_base64src';
	return new Promise((resolve, reject) => {
		const [, format, bodyData] = /data:image\/(\w+);base64,(.*)/.exec(base64data) || [];
		if (!format) {
			reject(new Error('ERROR_BASE64SRC_PARSE'));
		}
		const filePath = `${wx.env.USER_DATA_PATH}/${FILE_BASE_NAME}.${format}`;
		const buffer = wx.base64ToArrayBuffer(bodyData);
		fsm.writeFile({
			filePath,
			data: buffer,
			encoding: 'binary',
			success() {
				resolve(filePath);
			},
			fail() {
				reject(new Error('ERROR_BASE64SRC_WRITE'));
			},
		});
	});
};
// 时间戳转换为日期
function timestampToTime(timestamp, type, detail) {
	var date = new Date(timestamp); // 13位的时间戳
	var Y = date.getFullYear() + (type == 1 ? '-' : '年');
	var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + (type == 1 ? '-' : '月');
	var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + (type == 1 ? ' ' : '日');
	var h = date.getHours() + ':';
	var m = date.getMinutes() + ':';
	var s = date.getSeconds();
	if (detail === 1) {
		return Y + M + D + '' + h + m + s;
	}
	return Y + M + D;
}
// 时分秒转换成秒数
function convertToSeconds(timeStr) {
	const parts = timeStr.split(':');

	if (parts.length === 2) {
		// 分:秒
		const minutes = parseInt(parts[0], 10);
		const seconds = parseInt(parts[1], 10);

		return minutes * 60 + seconds;
	} else if (parts.length === 3) {
		// 时:分:秒
		const hours = parseInt(parts[0], 10);
		const minutes = parseInt(parts[1], 10);
		const seconds = parseInt(parts[2], 10);

		return hours * 3600 + minutes * 60 + seconds;
	} else {
		console.error('时间格式不正确');
	}
}
// 秒数转换成时分秒
function formatTime(seconds, type = 0) {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = seconds % 60;

	const formattedHours = String(hours).padStart(2, '0');
	const formattedMinutes = String(minutes).padStart(2, '0');
	const formattedSeconds = String(Math.floor(secs)).padStart(2, '0');
	// 动态调整格式
	if (type == 0) {
		if (hours > 0) {
			return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
		} else {
			return `${formattedMinutes}:${formattedSeconds}`;
		}
	} else {
		return `${formattedMinutes}分${formattedSeconds}秒`;
	}

}

function removeMilliseconds(timeStr) {
	// 使用正则表达式匹配小数点及其后面的部分，并替换为空字符串
	return timeStr.replace(/\..*$/, '');
}

function goBack() {
	if (/(Android)/i.test(navigator.userAgent)) {
		//判断Android
		callNative.finishCurrentPage();
	} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		//判断iPhone|iPad|iPod|iOS
		window.webkit.messageHandlers.finishCurrentPage.postMessage(null);
	}
}

function audioOpenPopup() {
	if (/(Android)/i.test(navigator.userAgent)) {
		//判断Android
		callNative.audioOpenPopup();
	} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		//判断iPhone|iPad|iPod|iOS
		window.webkit.messageHandlers.audioOpenPopup.postMessage(null);
	}
}

function audioClick() {
	if (/(Android)/i.test(navigator.userAgent)) {
		//判断Android
		callNative.audioClick();
	} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		//判断iPhone|iPad|iPod|iOS
		window.webkit.messageHandlers.audioClick.postMessage(null);
	}
}
function isPermissionsRecordAudioMethod() {  //1 有权限
	if (/(Android)/i.test(navigator.userAgent)) {
		//判断Android
		return callNative.isPermissionsRecordAudioMethod();
	} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		//判断iPhone|iPad|iPod|iOS
		window.webkit.messageHandlers.isPermissionsRecordAudioMethod.postMessage(null);
	}
}
function permissionsRecordAudioMethod() {  // 获取权限
	if (/(Android)/i.test(navigator.userAgent)) {
		//判断Android
		return callNative.permissionsRecordAudioMethod();
	} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		//判断iPhone|iPad|iPod|iOS
		window.webkit.messageHandlers.permissionsRecordAudioMethod.postMessage(null);
	}
}
function openVip() {  // 获取权限
	if (/(Android)/i.test(navigator.userAgent)) {
		//判断Android
		return callNative.openVip();
	} else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		//判断iPhone|iPad|iPod|iOS
		window.webkit.messageHandlers.openVip.postMessage(null);
	}
}




export default {
	encrypt,
	decrypt,
	base64Decode,
	md5Encrypt,
	throttle,
	toast,
	goPage,
	formatRichText,
	getStorage,
	setStorage,
	removeStorage,
	base64src,
	timestampToTime,
	convertToSeconds,
	formatTime,
	removeMilliseconds,
	goBack,
	audioClick,
	audioOpenPopup,
	isPermissionsRecordAudioMethod,
	permissionsRecordAudioMethod,
	openVip
}