<template>
  <view class="container">
    <!-- 书籍列表 -->
    <view class="book-grid">
      <view 
        class="book-item" 
        v-for="(book, index) in bookList" 
        :key="index"
        @click="handleBookClick(book)"
      >
        <image :src="book.cover_photo" class="book-image" mode="aspectFill"/>
        <view class="book-info">
          <image src="/static/icons/headset.png" class="book-icon"/>
          <text class="book-name">{{ book.title }}</text>
        </view>
      </view>
    </view>
	<uni-load-more v-if="totalNum > 0" :status="loadStatus" :content-text="{ contentdown: '加载更多', }" @tap="loadMore"/>
  </view>
</template>

<script>
import http from '/api/index.js'

export default {
  data() {
    return {
      dataParams: {
        limit: 20,
        page: 1
      },
	    totalNum: 0,
	    loadStatus: '',
      bookList: []
    }
  },
  onLoad() {
  	this.getList()
  },
  onReachBottom(){
    console.log('页面滚动到底部');
  	this.loadMore()
  },
  onUnload() {
  	console.log(window.history.state)
  	if(!window.history.state.back) {
  		this.goBack()
  	}
  },
  methods: {
	loadMore(){
		if (this.loadStatus !== 'noMore') {
			this.dataParams.page += 1;
			console.log("PAGE:::", this.dataParams.page)
			this.getList()
		}
	},
	getList() {
        this.loadStatus = 'loading'
        const params = {
            userkey: uni.getStorageSync('userkey') ,
            ...this.dataParams
        }
        
        console.log("入参：：：", params)
        http.getGuoxueList(params).then(res => {
            if (res.statusCode === 200) {
                console.log("出参：：：", res.data)
                const { lists, total } = res.data.data
                
                this.bookList = this.bookList.concat(...lists)
                this.totalNum = total
                this.loadStatus = this.bookList?.length < total ? 'more' : 'noMore';
            }
        })
	},
    handleBookClick(book) {
      // 处理书籍点击事件
      uni.navigateTo({
        url: `/pages/chineseClassics/lesson?id=${book.id}&title=${book.title}`
      })
    },
	goBack() {
		if (/(Android)/i.test(navigator.userAgent)) { //判断Android
		     console.log('执行app方法')
			callNative.finishCurrentPage();
		}
		else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) { //判断iPhone|iPad|iPod|iOS
			window.webkit.messageHandlers.finishCurrentPage.postMessage(null);
		}
	}
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 28rpx 24rpx calc(env(safe-area-inset-bottom) + 30rpx);
  box-sizing: border-box;

  .book-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 28rpx;

    .book-item {
      overflow: hidden;
      width: 216rpx;

      .book-image {
        width: 100%;
        height: 232rpx;
        border-radius: 20rpx;
      }

      .book-info {
        display: flex;
        align-items: center;
        padding: 20rpx 0;

        .book-icon {
          width: 28rpx;
          min-width: 28rpx;
          height: 28rpx;
        }

        .book-name {
          margin-left: 10rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>