<template>
	<view class="test-container">
		<!-- 状态栏占位 -->
		<!-- <view class="status-bar"></view> -->

		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<uni-icons class="nav-back" type="back" color="#333" size="24"></uni-icons>
			</view>
			<view class="nav-center center-all">
				<text class="nav-title">{{ currentQuestion }}/{{ questionList.length }}题</text>
			</view>
			<!-- <view class="nav-right" @click="skipQuestion">
				<text class="nav-skip">跳过</text>
			</view> -->
		</view>

		<!-- 题目区域 -->
		<view class="question-section">
			<span v-html="questionData?.stem?.html" class="question-text"></span>
		</view>
		<!-- {{ questionData?.stem.og.ogOps }} -->
		<!-- 选项区域 -->
		<!-- 小题tab和内容 -->
		<view v-if="questionData?.stem?.type === '复合题' && questionData?.stem?.sqs?.length > 1">
			<scroll-view scroll-x class="sub-question-tabs">
				<view
					v-for="(sub, idx) in questionData.stem.sqs"
					:key="idx"
					:class="['sub-tab', currentSubIndex === idx ? 'active' : '']"
					@click="switchSubQuestion(idx)"
				>
					{{ idx + 1 }}题
				</view>
			</scroll-view>
			<!-- 小题题干 -->
			<span v-html="questionData?.stem?.sqs[currentSubIndex]?.html" class="question-text" style="font-size:14px;color:#666;margin-bottom:8px;display:block;padding: 0 10px;"></span>
			<!-- 小题选项 -->
			<view class="options-section">
				<view v-for="(item, index) in questionData?.stem?.sqs[currentSubIndex]?.og?.ogOps" :key="index" class="option-item" :class="getOptionClass(index)" @click="selectOption(item, index)">
					<span class="option-text">{{ item.index }}.</span>
					<span v-html="item?.html"></span>
				</view>
			</view>
			<!-- 小题解析 -->
			<view class="analysis-section" v-if="showAnalysis">
				<view class="analysis-title">
					<text>解析:</text>
				</view>
				<view class="analysis-content">
					<span class="correct-answer">正确答案: {{ questionData?.answer?.anSqs[currentSubIndex]?.ans[0]?.html }}。</span>
					<span v-html="questionData?.explanation?.explanationSegs[currentSubIndex]?.html" class="analysis-text"></span>
				</view>
			</view>
		</view>

		<!-- 非复合题的普通选项和解析 -->
		<view v-else>
		<view class="options-section">
			<view v-for="(item, index) in questionData?.option" :key="index" class="option-item" :class="getOptionClass(index)" @click="selectOption(item, index)">
				<span class="option-text">{{ item.index }}.</span>
				<span v-html="item?.html"></span>
			</view>
		</view>
		<view class="analysis-section" v-if="showAnalysis">
			<view class="analysis-title">
				<text>解析:</text>
			</view>
			<view class="analysis-content">
				<span class="correct-answer">正确答案: {{ questionData?.answerOption }}。</span>
				<span v-html="questionData?.explanation?.explanationSegs[0]?.html" class="analysis-text"></span>
				</view>
			</view>
		</view>

		

		<!-- 底部按钮 -->
		<view class="bottom-section" v-if="isReviewMode || showNextButton">
			<view class="nav-buttons">
				<view class="next-button"  @click="debouncedNextQuestion">
					<text class="next-text">下一题</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import http from '/api/index.js';
import Tools from '/utils/index.js';
import QuestionParserService from '/static/xkw-xop-js-qbmsdk-1.0.3/xkw-xop-qbmsdk.js';

export default {
	data() {
		return {
			pageData: '',
			range_key: '',
			questionList: [],
			currentQuestion: 1,
			selectedOption: '', // 用户选择的答案
			selectedIndex: '', // 用户选择的选项索引
			showAnalysis: false, // 是否显示解析
			hasAnswered: false, // 是否已经答题
			questionData: {},
			// 答题状态记录 - 记录每道题的答题情况
			answerRecords: {}, // 格式: { questionIndex: { selectedOption, selectedIndex, hasAnswered, showAnalysis } }
			currentSubIndex: 0, // 当前复合题小题索引
			showNextButton: false, // 是否显示“下一题”按钮
			isReviewMode: false, // 是否为查看模式
			isNextButtonDisabled: false, // 添加防抖状态变量
		};
	},
	onLoad(data) {
		this.pageData = data;
		// 记录测试开始时间
		uni.setStorageSync('TEST_START_TIME', Date.now());

		// 检查是否是查看模式（从结果页跳转）
		const jumpTo = uni.getStorageSync('JUMP_TO_QUESTION');
		if (jumpTo) {
			this.isReviewMode = true;
			uni.removeStorageSync('JUMP_TO_QUESTION')
		}

		// 检查是否是重做测试
		const isRetry = data.retry === 'true';

		if (isRetry) {
			// 重做测试时，从本地存储恢复题目列表
			this.questionList = uni.getStorageSync('TEST_QUESTION_LIST') || [];
			if (this.questionList.length > 0) {
				this.questionData = this.questionList[0];
				// 重置答题状态
				this.answerRecords = {};
				this.currentQuestion = 1;
				this.selectedOption = '';
				this.selectedIndex = '';
				this.hasAnswered = false;
				this.showAnalysis = false;
				this.currentSubIndex = 0; // 重置复合题小题索引
				// 不重置查看模式状态，而是根据 JUMP_TO_QUESTION 来决定
				if (!uni.getStorageSync('JUMP_TO_QUESTION')) {
					this.isReviewMode = false;
					this.showNextButton = false;
				}
			}
		} else {
			// 首次进入，正常获取题目列表
			this.getQuestionList(data);
		}
		console.log(	this.isReviewMode, '	this.isReviewMode')

		// 监听跳转到指定题目的事件
		uni.$on('jumpToQuestion', (questionNumber) => {
			this.jumpToSpecificQuestion(questionNumber);
		});

		// 监听重做测试事件
		uni.$on('retryTest', () => {
			this.resetTestState();
		});
	},
	onUnload() {
		// 页面卸载时移除事件监听
		uni.$off('jumpToQuestion');
		uni.$off('retryTest');
	},
	methods: {
		getQuestionList(data) {
			let queryData = {
				userkey: data.userkey,
				textbook_id: data.textbook_id,
				catalog_id: [data.catalog_id]
				// session_id: ''
			};
			http.getTakeTest(queryData).then((res) => {
				console.log(res);
				this.range_key = res.data.data.range_key;
				let lists = res.data.data.lists;
				lists = lists.filter((item)=>{
					return item.type.name != '听句子或对话判断';
				});
				const parser = new QuestionParserService();
				lists.map((item) => {
					// item.stem1 = item.stem;
					item.stem = parser.splitStem(item.stem);
					item.answer = parser.splitAnswer(item.answer);
					item.explanation = parser.splitExplanation(item.explanation);
					this.$nextTick(()=>{
						item.option = item.stem?.og?.ogOps;
						item.answerOption = item.answer.anSqs[0].ans[0]?.html;
					})
					
				});
				this.questionList = lists;
				this.questionData = lists[0];

				// 初始化后恢复第一题的答题状态
				this.restoreAnswerRecord();

				// console.log(this.questionList);
				console.log(lists);
			console.log(this.questionData);

			});
		},
		// 返回上一页
		goBack() {
			Tools.goBack();
		},

		// 跳过题目
		skipQuestion() {
			if (this.currentQuestion >= this.questionList.length) {
				// 最后一题，跳转结果页
				this.goToResultPage();
			} else {
				this.nextQuestion();
			}
		},

		// 选择选项
		selectOption(item, index) {
			if (!this.hasAnswered) {
				this.selectedOption = item.index;
				this.selectedIndex = index;
				this.hasAnswered = true;

				// 查看模式下不执行答题逻辑，只显示解析
				if (this.isReviewMode) {
					setTimeout(() => {
						this.showAnalysis = true;
						this.saveAnswerRecord();
						// 查看模式下保持下一题按钮显示
						this.showNextButton = true;
					}, 300);
					return;
				}

				// 立即判断对错
				const isCorrect = this.selectedOption === (this.questionData?.stem?.type === '复合题' ? this.questionData?.answer?.anSqs[this.currentSubIndex]?.ans[0]?.html : this.questionData?.answerOption);

				// 延迟显示解析
				setTimeout(() => {
					this.showAnalysis = true;
					this.saveAnswerRecord();

					if (isCorrect) {
						this.showNextButton = false; // 答对不显示下一题按钮
						setTimeout(() => {
							this.nextQuestion();
						}, 800);
					} else {
						// 复合题不直接显示下一题按钮
						if (this.questionData?.stem?.type === '复合题') {
							// 判断所有小题是否都答完
							this.showNextButton = this.isAllSubQuestionsAnswered();
						} else {
							this.showNextButton = true;
						}
					}
				}, 300);
			}
		},

		// 确定答案
		confirmAnswer() {
			console.log(this.questionData);
			if (this.selectedOption && !this.hasAnswered) {
				this.hasAnswered = true;
				// 延迟显示解析，增加用户体验
				setTimeout(() => {
					this.showAnalysis = true;
					// 保存当前题目的答题状态
					this.saveAnswerRecord();
				}, 300);
			}
		},
		submitQuestion() {
			let queryData = {
				userkey: this.pageData.userkey,
				range_key: this.range_key,
				question_id: this.questionData.id,
				answer: this.selectedOption,
				result: this.selectedOption == this.questionData?.answerOption ? 1 : 2
				
			};
			if(this.questionData?.stem?.type === '复合题' ) {
				queryData['sub_question'] =  this.currentSubIndex + 1
			}

			console.log(queryData);
			http.submitQuestion(queryData).then((res) => {
				console.log(res);
			});
		},

		// 保存答题记录
		saveAnswerRecord() {
			const key = this.questionData?.stem?.type === '复合题' ? `${this.currentQuestion}_${this.currentSubIndex}` : this.currentQuestion;
			this.answerRecords[key] = {
				selectedOption: this.selectedOption,
				selectedIndex: this.selectedIndex,
				hasAnswered: this.hasAnswered,
				showAnalysis: this.showAnalysis
			};
			this.submitQuestion();
			console.log('保存答题记录:', key, this.answerRecords[key]);
		},

		// 恢复答题记录
		restoreAnswerRecord() {
			const key = this.questionData?.stem?.type === '复合题' ? `${this.currentQuestion}_${this.currentSubIndex}` : this.currentQuestion;
			const record = this.answerRecords[key];
			
			// 首先检查是否为查看模式，如果是则直接设置按钮为显示
			if (this.isReviewMode) {
				this.showNextButton = true;
				
				// 如果有记录，恢复其他状态
				if (record) {
					this.selectedOption = record.selectedOption;
					this.selectedIndex = record.selectedIndex;
					this.hasAnswered = record.hasAnswered;
					this.showAnalysis = record.showAnalysis;
				} else {
					// 重置为未答题状态，但保持按钮显示
					this.selectedOption = '';
					this.selectedIndex = '';
					this.hasAnswered = false;
					this.showAnalysis = false;
				}
				return; // 查看模式下直接返回，不执行后续逻辑
			}
			
			// 非查看模式的原有逻辑
			if (record) {
				this.selectedOption = record.selectedOption;
				this.selectedIndex = record.selectedIndex;
				this.hasAnswered = record.hasAnswered;
				this.showAnalysis = record.showAnalysis;
				if (this.hasAnswered && this.showAnalysis) {
					const isCorrect = this.selectedOption === (this.questionData?.stem?.type === '复合题' ? this.questionData?.answer?.anSqs[this.currentSubIndex]?.ans[0]?.html : this.questionData?.answerOption);
					if (!isCorrect) {
						this.showNextButton = true;
					} else {
						this.showNextButton = false;
					}
				} else {
					this.showNextButton = false;
				}
				// 判断是否所有小题都答完，决定"下一题"按钮显示
				if (this.questionData?.stem?.type === '复合题') {
					this.showNextButton = this.isAllSubQuestionsAnswered();
				}
			} else {
				// 重置为未答题状态
				this.selectedOption = '';
				this.selectedIndex = '';
				this.hasAnswered = false;
				this.showAnalysis = false;
				this.showNextButton = false;
			}
		},

		// 获取选项样式类
		getOptionClass(index) {
			if (this.questionData?.stem?.type === '复合题') {
				// 复合题：取当前小题的选项
				const currentOption = this.questionData?.stem?.sqs?.[this.currentSubIndex]?.og?.ogOps?.[index];
			if (!currentOption) return 'option-normal';
				const currentOptionIndex = currentOption.index;
				const answerOption = this.questionData?.answer?.anSqs?.[this.currentSubIndex]?.ans[0]?.html;
				// 未答题
			if (!this.hasAnswered) {
				return currentOptionIndex === this.selectedOption ? 'option-selected' : 'option-normal';
			}
				// 已答题未显示解析
			if (!this.showAnalysis) {
				return currentOptionIndex === this.selectedOption ? 'option-selected' : 'option-normal';
			}
				// 显示解析
				if (currentOptionIndex === answerOption) {
				return 'option-correct';
				} else if (currentOptionIndex === this.selectedOption && this.selectedOption !== answerOption) {
					return 'option-wrong';
				} else {
					return 'option-normal';
				}
			} else {
				// 普通题
				const currentOption = this.questionData?.option?.[index];
				if (!currentOption) return 'option-normal';
				const currentOptionIndex = currentOption.index;
				const answerOption = this.questionData?.answerOption;
				if (!this.hasAnswered) {
					return currentOptionIndex === this.selectedOption ? 'option-selected' : 'option-normal';
				}
				if (!this.showAnalysis) {
					return currentOptionIndex === this.selectedOption ? 'option-selected' : 'option-normal';
				}
				if (currentOptionIndex === answerOption) {
					return 'option-correct';
				} else if (currentOptionIndex === this.selectedOption && this.selectedOption !== answerOption) {
				return 'option-wrong';
				} else {
					return 'option-normal';
			}
			}
		},

		// 上一题
		prevQuestion() {
			if (this.currentQuestion > 1) {
				// 保存当前题目的答题状态（如果已经答题）
				if (this.hasAnswered) {
					this.saveAnswerRecord();
				}

				this.currentQuestion--;

				// 加载上一题数据
				this.loadQuestion();

				// 恢复上一题的答题状态
				this.restoreAnswerRecord();
			}
		},

		// 下一题
		nextQuestion() {
			// 复合题：所有小题都答完才能跳下一大题
			if (this.questionData?.stem?.type === '复合题') {
				let allSubAnswered = true;
				for (let i = 0; i < this.questionData.stem.sqs.length; i++) {
					const subKey = `${this.currentQuestion}_${i}`;
					if (!this.answerRecords[subKey] || !this.answerRecords[subKey].hasAnswered) {
						allSubAnswered = false;
						break;
					}
				}
				if (!allSubAnswered) {
					// uni.showToast({ title: '请先完成所有小题', icon: 'none' });
					return;
				}
			}
			// 只跳到下一道大题，不再切小题
			if (this.currentQuestion < this.questionList.length) {
				if (this.hasAnswered) {
					this.saveAnswerRecord();
				}
				this.currentQuestion++;
				this.currentSubIndex = 0; // 切换大题时小题索引归零
				this.loadQuestion();
				this.updateSubQuestionData();
				this.restoreAnswerRecord();
				if (this.isReviewMode) {
					this.showNextButton = true;
				}
			} else {
				// 最后一题，跳转结果页
				this.goToResultPage();
			}
		},

		// 跳转到结果页
		goToResultPage() {
			// 记录测试结束时间
			uni.setStorageSync('TEST_END_TIME', Date.now());

			// 保存答题记录到本地存储
			uni.setStorageSync('TEST_ANSWER_RECORDS', this.answerRecords);
			uni.setStorageSync('TEST_QUESTION_LIST', this.questionList);

			uni.navigateTo({
				url: '/pages/takeTest/resultPage?title=' + this.pageData.title
			});
		},

		// 加载题目数据
		loadQuestion() {
			// 这里应该从服务器或本地数据加载题目
			// 现在只是模拟数据
			this.questionData = this.questionList[this.currentQuestion - 1];
			this.currentSubIndex = 0;
			this.updateSubQuestionData();
			// console.log('加载第', this.currentQuestion, '题');
			console.log(this.questionData);
			
			// 只在非查看模式下重置按钮状态
			if (!this.isReviewMode) {
				this.showNextButton = false;
			} else {
				this.showNextButton = true;
			}
		},

		// 跳转到指定题目
		jumpToSpecificQuestion(questionNumber) {
			if (questionNumber >= 1 && questionNumber <= this.questionList.length) {
				this.isReviewMode = true; // 保证查看模式
				this.currentQuestion = questionNumber;
				this.loadQuestion();
				this.restoreAnswerRecord();
			}
		},

		// 重置测试状态
		resetTestState() {
			this.answerRecords = {};
			this.currentQuestion = 1;
			this.currentSubIndex = 0;
			this.selectedOption = '';
			this.selectedIndex = '';
			this.hasAnswered = false;
			this.showAnalysis = false;
			this.showNextButton = false; // 重置下一题按钮状态
			this.isReviewMode = false; // 重置查看模式
			this.isNextButtonDisabled = false; // 重置防抖状态
			if (this.questionList.length > 0) {
				this.questionData = this.questionList[0];
				this.updateSubQuestionData();
			}
		},
		switchSubQuestion(idx) {
			this.currentSubIndex = idx;
			this.updateSubQuestionData();
			this.restoreAnswerRecord();
			// 判断是否所有小题都答完，决定"下一题"按钮显示
			if (!this.isReviewMode) {
				this.showNextButton = this.isAllSubQuestionsAnswered();
			} else {
				this.showNextButton = true;
			}
		},
		updateSubQuestionData() {
			if (this.questionData?.stem?.type === '复合题') {
				// 题干、选项、答案等都通过currentSubIndex在模板中动态取，无需再赋值
				// 但可在此处做一些预处理（如有需要）
			}
		},
		// 新增：判断是否为查看模式
		checkReviewMode() {
			// 仅在onLoad时设置isReviewMode，后续不自动变true
			// 保留空实现或直接删除该方法
		},
		isAllSubQuestionsAnswered() {
			if (this.questionData?.stem?.type !== '复合题') return true;
			for (let i = 0; i < this.questionData.stem.sqs.length; i++) {
				const subKey = `${this.currentQuestion}_${i}`;
				if (!this.answerRecords[subKey] || !this.answerRecords[subKey].hasAnswered) {
					return false;
				}
			}
			return true;
		},
		// 添加防抖包装方法
		debouncedNextQuestion() {
			if (this.isNextButtonDisabled) return; // 如果按钮已禁用，直接返回
			
			// 设置按钮为禁用状态
			this.isNextButtonDisabled = true;
			
			// 执行原始的下一题方法
			this.nextQuestion();
			
			// 800毫秒后重新启用按钮
			setTimeout(() => {
				this.isNextButtonDisabled = false;
			}, 400);
		}
	}
};
</script>

<style lang="scss" scoped>
.test-container {
	min-height: 100vh;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	padding: 30px 0 60px;
}

/* 状态栏占位 */
.status-bar {
	height: 44px;
	background-color: #ffffff;
}

/* 导航栏 */
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 30rpx;
	padding-top: calc(20rpx + var(--status-bar-height));
	background-color: #fff;
	z-index: 1000;
	// box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.nav-left {
	position: absolute;
	left: 30rpx;
	width: 60px;
	height: 20px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
}

.nav-center {
	flex: none;
}

.nav-title {
	font-size: 16px;
	color: #333333;
	font-weight: 500;
}

.nav-skip {
	font-size: 14px;
	color: #666666;
}

/* 题目区域 */
.question-section {
	padding: 20px;
	background-color: #ffffff;
}

.question-text {
	font-size: 16px;
	color: #333333;
	line-height: 1.5;
	font-weight: 500;
	vertical-align: middle;

	// 确保题目中的图片能够自适应显示
	:deep(img) {
		max-width: 100% !important;
		// height: auto !important;
		// display: block;
		display: inline-block!important;

		// margin: 8px 0;
		vertical-align: middle;

	}
	:deep(table) {
		max-width: 100% !important;

	}
	// :deep(.xkw-math-img) {
	// 	max-width: 100% !important;
	// 	height: auto !important;
	// 	// display: inline-block;
	// 	vertical-align: middle;
	// 	border: 1px solid red;
	// }

	// 确保p标签内容不会溢出
	:deep(p) {
		max-width: 100%;
		overflow-x: auto;
		margin: 8px 0;
	}
}

/* 选项区域 */
.options-section {
	padding: 0 20px;
}

.option-item {
	margin-bottom: 15px;
	padding: 15px;
	border-radius: 8px;
	border: 1upx solid #e0e0e0;
	background-color: #ffffff;
	transition: all 0.3s ease;
	position: relative;
	vertical-align: middle;

	&:active {
		transform: scale(0.98);
	}

	// 处理选项中的HTML内容，特别是图片
	:deep(img) {
		max-width: 100% !important;
		// height: auto !important;
		// display: block;
		display: inline-block!important;

		// margin: 8px 0;
		vertical-align: middle;


	}
	// :deep(.xkw-math-img) {
	// 	max-width: 100% !important;
	// 	min-width: 20px;
	// 	height: auto !important;
	// 	// display: inline-block;
	// 	vertical-align: middle;
	// 	border: 1px solid red;
	// }
	:deep(p) {
		max-width: 100% !important;
		overflow-x: auto;
		margin: 5px 0;
	}
}

.option-normal {
	border-color: #e0e0e0;
	background-color: #ffffff;
}

.option-selected {
	border-color: #007aff;
	background-color: #f0f8ff;
}

.option-correct {
	border-color: #4caf50;
	background-color: #f1f8e9;
}

.option-wrong {
	border-color: #f44336;
	background-color: #ffebee;
}

.option-text {
	font-size: 14px;
	color: #333333;
	line-height: 1.6;
}

/* 解析区域 */
.analysis-section {
	padding: 20px;
	background-color: #f8f8f8;
	margin: 0 20px 20px 20px;
	border-radius: 8px;
	// border: 1px solid
}

.analysis-title {
	margin-bottom: 10px;
}

.analysis-title text {
	font-size: 16px;
	color: #f59c12;
	font-weight: 600;
}

.analysis-content {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.correct-answer {
	font-size: 14px;
	color: #333333;
	font-weight: 500;
}

.analysis-text {
	font-size: 14px;
	color: #666666;
	line-height: 1.6;
	white-space: pre-line;

	// 处理解析中的HTML内容，特别是图片
	:deep(img) {
		max-width: 100% !important;
		height: auto !important;
		display: inline-block;
		margin: 8px 0;
	}

	:deep(p) {
		max-width: 100% !important;
		overflow-x: auto;
		margin: 5px 0;
	}
}

/* 底部按钮 */
.bottom-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	z-index: 100;
}

.nav-buttons {
	display: flex;
	justify-content: center;
}

.next-button {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ff9500;
	box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);

	&:active {
		transform: scale(0.95);
		box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.4);
	}
}

.next-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: 500;
}
/* 全局图片样式 - 确保所有图片都能正确显示 */
img {
	max-width: 100% !important;
	height: auto !important;
	display: block;
	margin: 8px auto;
}

/* 全局p标签样式 - 防止内容溢出 */
p {
	width: 100%;
	overflow-x: auto;
	word-wrap: break-word;
	word-break: break-all;
}

.sub-question-tabs {
	display: flex;
	margin-bottom: 10px;
	padding: 10px;
	white-space: nowrap;
	overflow-x: auto;
	background: #fff;
}
.sub-tab {
	padding: 6px 16px;
	margin-right: 8px;
	border-radius: 16px;
	background: #f0f0f0;
	color: #333;
	cursor: pointer;
	font-size: 14px;
	transition: background 0.2s;
	white-space: nowrap;
	overflow: hidden;
	display: inline-block;
}
.sub-tab.active {
	background: #ff9500;
	color: #fff;
}
/* 添加禁用状态样式 */
.next-button.disabled {
	opacity: 0.6;
	pointer-events: none; /* 禁止点击事件 */
}
</style>