<template>
	<view class="lesson-page">
		<view class="textbook-content">
			<image class="content-bg" src="/static/icons/title_bg2.png" />
			<view class="book-info">
				<view class="grade-text">{{ book?.grade }}</view>
				<view class="grade-text">{{ book?.semester }}</view>
				<view class="btn-content" @click="navigateToBookList">
					<image class="change-btn" src="/static/icons/change.png" />
					切换课本
				</view>
			</view>
		</view>

		<lesson-list comeFrom="learningGoal" :units="unitList" />
	</view>
</template>

<script>
	import LessonList from '@/components/LessonList.vue';
	import http from '/api/index.js'

	export default {
		components: {
			LessonList
		},
		data() {
			return {
				book: {},
				details: {},
				unitList: []
			};
		},
		onLoad(options) {
			this.book = options
			this.getDetails()
		},
		onUnload() {
			console.log(window.history.state)
			if(!window.history.state.back) {
				this.goBack()
			}
		},
		methods: {
			navigateToBookList() {
				uni.navigateTo({
					url: '/pages/wordSpell/bookList?comeFrom=learningGoal'
				});
			},
			getDetails() {
				const params = {
					userkey: uni.getStorageSync('userkey') ,
					book_id: this.book.id
				}
				console.log("入参：：：", params)

				http.getXuexiMubiaoCourseList(params).then(res => {
					if (res.statusCode === 200) {
						console.log("出参：：：", res.data)
						this.unitList = res.data.data;
					}
				})
			},
			goBack() {
				if (/(Android)/i.test(navigator.userAgent)) { //判断Android
				     console.log('执行app方法')
					callNative.finishCurrentPage();
				}
				else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) { //判断iPhone|iPad|iPod|iOS
					window.webkit.messageHandlers.finishCurrentPage.postMessage(null);
				}
			} 
			
		},
	};
</script>

<style lang="scss" scoped>
	.lesson-page {
		background-color: #f7f7f7;
		position: relative;
		width: 100%;
		min-height: 100vh;
		padding: 30rpx 24rpx;
		box-sizing: border-box;
	}

	.textbook-content {
		position: relative;
		height: 208rpx;
		width: 100%;
		background: #ffe476;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.content-bg {
		position: absolute;
		width: 100%;
		height: 100%;
		z-index: 0;
	}

	.book-info {
		position: absolute;
		right: 20px;
		top: 55%;
		width: 300upx;
		transform: translateY(-50%);
		text-align: center;
	}

	.grade-text {
		position: static;
		font-size: 32upx;
		color: #333;
		font-family: PingFang SC-Medium;
		font-weight: 500;
		line-height: 30upx;
	}

	.grade-text:nth-child(2) {
		font-size: 26upx;
		margin-top: 4px;
		font-weight: 0;
		color: #555;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		width: 100%;
	}

	.btn-content {
		margin: 8px auto;
		background: #ffffff;
		border-radius: 30upx;
		padding: 4px 12px;
		width: fit-content;
		height: auto;
		font-size: 24upx;
		color: #f59c12;
		border: 1px solid #f59c12;
		cursor: pointer;
		position: relative;
		padding-left: 55upx;
	}

	.change-btn {
		width: 26upx;
		height: 26upx;
		position: absolute;
		left: 20upx;
		top: 50%;
		transform: translateY(-50%);
		/* vertical-align: baseline; */
		/* margin-top: 4upx; */
	}
</style>