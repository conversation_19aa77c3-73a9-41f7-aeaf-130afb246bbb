<template>
  <view class="page-container">
    <!-- 句子列表 -->
    <view class="sentence-list">
      <view 
        v-for="(item, index) in sentences" 
        :key="index"
        class="sentence-card"
        :class="{ 'highlighted': item.highlighted, 'recording': item.recording }"
        @click="selectSentence(index)"
      >
        <view class="sentence-content">
          <text class="sentence-text" :style="{ color: item.textColor }">
            {{ item.text }}
          </text>
          <!-- 录音按钮 (仅第一个句子显示) -->
          <view v-if="item.showButtons" class="button-group">
            <view class="record-btn original" @click.stop="playOriginal(index)">
                
		    	<uni-icons type="sound-filled" color="#fff" ></uni-icons>
              <text class="btn-text">播放原音</text>
            </view>
            <view class="record-btn recorded" @click.stop="playRecorded(index)">
            <uni-icons type="mic-filled" color="#fff" ></uni-icons>

              <text class="btn-text">播放录音</text>
            </view>
          </view>
        </view>
        <view class="score" :style="{ color: item.scoreColor }">
          {{ item.recording ? '' : item.score }}
        </view>
        <!-- 录音中的加载动画 -->
        <view v-if="item.recording" class="loading-spinner"></view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="bottom-btn" @click="reRecord">
        <view class="btn-icon-wrapper">
          <image class="btn-icon-img" src="/static/images/record/reStart.png" mode="aspectFit"></image>
        </view>
        <text class="btn-label">重录</text>
      </view>

      <view class="bottom-btn center-btn" @click="toggleRecording">
        <view class="btn-icon-wrapper center-icon" :class="{ 'recording': isRecording }">
          <image class="btn-icon-img" src="/static/images/record/pause.png" mode="aspectFit"></image>
        </view>
        <text class="btn-label">{{ isRecording ? '录音中...' : '点击话筒开始录制' }}</text>
      </view>

      <view class="bottom-btn" @click="submit">
        <view class="btn-icon-wrapper">
          <image class="btn-icon-img" src="/static/images/record/submit.png" mode="aspectFit"></image>
        </view>
        <text class="btn-label">提交</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isRecording: false,
      currentSentence: 0,
      sentences: [
        {
          text: 'Unit one Hello!',
          score: 98,
          textColor: '#4CAF50',
          scoreColor: '#4CAF50',
          highlighted: true,
          showButtons: true,
          recording: false
        },
        {
          text: "Hello! I'm Zoom.",
          score: 40,
          textColor: '#4CAF50',
          scoreColor: '#FF5722',
          highlighted: false,
          showButtons: false,
          recording: false
        },
        {
          text: "Hello! I'm Mike.",
          score: 0,
          textColor: '#FF5722',
          scoreColor: '#FF5722',
          highlighted: false,
          showButtons: false,
          recording: false
        },
        {
          text: "Hi, I'm Wu Yifan.",
          score: 98,
          textColor: '#4CAF50',
          scoreColor: '#4CAF50',
          highlighted: false,
          showButtons: false,
          recording: false
        },
        {
          text: 'HELLO!',
          score: 98,
          textColor: '#4CAF50',
          scoreColor: '#4CAF50',
          highlighted: false,
          showButtons: false,
          recording: false
        },
        {
          text: "Hi! My name's Zip.",
          score: 60,
          textColor: '#FF5722',
          scoreColor: '#FF9800',
          highlighted: false,
          showButtons: false,
          recording: false
        },
        {
          text: 'Goodbye!',
          score: 98,
          textColor: '#4CAF50',
          scoreColor: '#4CAF50',
          highlighted: false,
          showButtons: false,
          recording: false
        },
        {
          text: "Hello, I'm Chen Jie. What's your name?",
          score: 80,
          textColor: '#FF9800',
          scoreColor: '#FF9800',
          highlighted: true,
          showButtons: false,
          recording: false
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    selectSentence(index) {
      // 重置所有句子状态
      this.sentences.forEach((item, i) => {
        item.highlighted = i === index
        item.showButtons = i === index
      })
      this.currentSentence = index
    },
    playOriginal(index) {
      console.log('播放原音:', this.sentences[index].text)
      // 实现播放原音逻辑
    },
    playRecorded(index) {
      console.log('播放录音:', this.sentences[index].text)
      // 实现播放录音逻辑
    },
    toggleRecording() {
      this.isRecording = !this.isRecording
      
      if (this.isRecording) {
        // 开始录音
        this.sentences[this.currentSentence].recording = true
        console.log('开始录音')
        
        // 模拟录音过程
        setTimeout(() => {
          this.isRecording = false
          this.sentences[this.currentSentence].recording = false
          console.log('录音结束')
        }, 3000)
      }
    },
    reRecord() {
      console.log('重新录音')
      // 实现重录逻辑
    },
    submit() {
      console.log('提交录音')
      // 实现提交逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  padding-bottom: 170rpx;
}


.sentence-list {
  padding: 20rpx;
}

.sentence-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  
  &.highlighted {
    border: 2rpx solid #FFB300;
  }
  
  .sentence-content {
    flex: 1;
    
    .sentence-text {
      font-size: 32rpx;
      line-height: 1.4;
      margin-bottom: 20rpx;
    }
    
    .button-group {
      display: flex;
      gap: 20rpx;
      
      .record-btn {
        display: flex;
        align-items: center;
        padding: 12rpx 24rpx;
        border-radius: 40rpx;
        
        &.original {
          background-color: #FFB300;
        }
        
        &.recorded {
          background-color: #FFB300;
        }
        
        .btn-icon {
          font-size: 24rpx;
          margin-right: 8rpx;
        }
        
        .btn-text {
          font-size: 24rpx;
          color: #fff;
        }
      }
    }
  }
  
  .score {
    font-size: 36rpx;
    font-weight: bold;
    min-width: 60rpx;
    text-align: right;
  }
  
  .loading-spinner {
    width: 40rpx;
    height: 40rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #FFB300;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #eee;
  
  .bottom-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .btn-icon-wrapper {
    //   width: 80rpx;
    //   height: 80rpx;
    //   border-radius: 50%;
    //   background-color: #f0f0f0;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   margin-bottom: 10rpx;
      
      &.center-icon {
        width: 100rpx;
        height: 100rpx;
        background-color: #FFB300;
        
        &.recording {
          background-color: #FF5722;
        }
      }
      
      .btn-icon {
        font-size: 36rpx;
        color: #666;
      }

      .btn-icon-img {
        width: 36rpx;
        height: 36rpx;
      }
    }

    &.center-btn .btn-icon-wrapper {
      .btn-icon {
        color: #fff;
        font-size: 40rpx;
      }

      .btn-icon-img {
        width: 40rpx;
        height: 40rpx;
      }
    }
    
    .btn-label {
      font-size: 24rpx;
      color: #666;
      text-align: center;
    }
  }
}
</style>