<template>
  <view class="word-list">
	  <view class="result-title-con">
		   <img class="result-bg1" src="/static/icons/result_bg1.png" alt="">
		   <view class="word-tips">
			   <view>恭喜~</view>
			   <view class="tips ">词语听写已完成</view>
		   </view>
	  </view>
    
    <!-- 课文信息区域 -->
    <view class="lesson-info">
      <view class="lesson-title-wrap">
        <text class="lesson-title">{{ words.class_name }}</text>
      </view>
      
      <!-- 词数和操作区 -->
      <view class="word-count-bar">
        <text class="word-count">共 {{ words.selectedWords && words.selectedWords.length }} 词</text>
      </view>

      <!-- 词语网格 -->
      <view class="word-grid">
        <view 
          v-for="(wordObj, index) in words.selectedWords" 
          :key="index"
          class="word-item"
        >
          <view class="pinyin-row">
            <view v-for="(py, idx) in wordObj.pinyin" :key="idx" class="pinyin">{{ py }}</view>
          </view>
          <view class="word-content">
            <view class="hanzi-row">
              <view v-for="(hz, idx) in (wordObj.word || wordObj.phrase)" :key="idx" class="hanzi">{{ hz }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn-wrap">
      <button class="pause-btn" @tap="rePlay">
         重新听写
      </button>
    </view>
  </view>
</template>

<script>
import Tools from '/utils/index.js'
export default {
 
  data() {
    return {
      words: {}
    }
  },
  created() {
	  this.words = Tools.getStorage('WORDLIST_SPELL');
  },
  onBackPress(options) {
  	  if(options && options.from == 'backbutton') { // 点击返回按钮
		// 判断当前页面栈长度，然后决定返回几级
		const pages = getCurrentPages();
		
		if (pages.length > 2) {
		    uni.navigateBack({
		        delta: 2 // 返回两级
		    });
		} else {
		    // 处理返回一级或关闭应用的情况
		    uni.navigateBack({
		        delta: pages.length - 1
		    });
		}
  	  }
  },
  methods: {
	rePlay() {
		uni.navigateTo({
			url: '/pages/wordSpell/wordList'
		});
	}
  }
}
</script>

<style lang="scss" scoped>
.word-list {
  min-height: calc(100vh - 44px - 190rpx);
  background: #72CF67;
  padding: 30rpx 24rpx 160rpx;
}
.result-title-con {
	 position: absolute;
	 top: 30upx;
	 left: 56upx;
	 display: flex;
	.result-bg1 {
		width: 200upx;
		height: 200upx;
		vertical-align: middle;
	}
	.word-tips {
		margin-left: 30upx;
		font-size: 40upx;
		color: #FFFFFF;
		margin-top: 30upx;
	   .tips {
		   margin-top: 10upx;
		   font-size: 32upx;
	   }
	}
}

.lesson-info {
  background-color: #FFF;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  margin-top: 170upx
}

.lesson-title-wrap {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .lesson-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    line-height: 32rpx;
  }
}

.word-count-bar {
  display: flex;
  justify-content:center;
  align-items: center;
  margin-bottom: 40rpx;

  .word-count {
    flex: 1;
    font-size: 24rpx;
    color: #999;
	text-align: center;
  }

}

.word-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 28rpx;
  
    .selected {
      background-color: #FFECCC!important;
    }


  .word-item {
    flex-grow: 0;
    flex-shrink: 0;
    transition: all 0.2s ease;
	margin-bottom: 15upx;


    .word-content {
      display: flex;
      flex-direction: column;
      align-items: center;
	  line-height: 42rpx;
	  background-color: #FFECCC;
	  border-radius: 20rpx;
	  padding: 16rpx 34rpx;
	  min-width: 70rpx;
    }

    .pinyin-row {
      display: flex;
      gap: 12rpx;
      margin-bottom: 4rpx;
	  justify-content: center;
      
      .pinyin {
        font-size: 24rpx;
        color: #333;
        line-height: 1.2;
		text-align: center;
      }
    }

    .hanzi-row {
      display: flex;
      gap: 12rpx;
      
      .hanzi {
        font-size: 36rpx;
        color: #333;
        font-family: 'Kaiti TC';
        font-weight: normal;
      }
    }
  }
}

.bottom-btn-wrap {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 40rpx;
  padding: 0 140rpx;
  display: flex;
  justify-content: center;
  
  .pause-btn {
    width: 100%;
    height: 72rpx;
    background: #FFFFFF;
    border-radius: 30px;
    font-size: 28upx;
    color: #F59C12;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    
    &::after {
      border: none;
    }
    
    &:active {
      opacity: 0.8;
    }
  }
}

</style>
