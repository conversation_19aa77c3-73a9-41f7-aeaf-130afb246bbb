import {
	defineStore
} from 'pinia'


export const essayStore = defineStore('essay', {
	state: () => ({
		currentEssay: {
			usekey: uni.getStorageSync('userkey'),
			title: "责任与担当",
			content: "积极有为 一代人",
			xueke: "语文",
			wenti: "记叙文",
			type: "2",
			grade: "高中",
			zishu: "800",
			token: ''
		}
	}),
	actions: {
		async saveEssay(data) {
			this.currentEssay = data;


		}
	},
	getters: {
		userEssays: (state) => {
			// const userStore = useUserStore()
			// return state.essays.filter(essay => essay.author === userStore.username)
		}
	}
})