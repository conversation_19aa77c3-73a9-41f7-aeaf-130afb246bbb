.center-all {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-vertical {
  display: flex;
  align-items: center;
}

.center-horizontal {
  display: flex;
  justify-content: center;
}

.between-vertical {
  display: flex;
  justify-content: space-between;
}

.oellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

.oellipsis3 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

button::after {
  border: none;
}

.clearFix {
  clear: both;
}

.dis-block {
  display: block;
  width: 100%;
  min-height: 30px;
  text-align: right;
}

.f36 {
  font-size: 36px;
}

.f34 {
  font-size: 34px;
}

.f32 {
  font-size: 32px;
}

.f30 {
  font-size: 30px;
}

.f28 {
  font-size: 28px;
}

.f14 {
  font-size: 14px;
}

.orange-color {
  color: #ED9126;
}

.navbar-right-btn {
  float: right;
  font-weight: 400;
  font-size: 28rpx;
  color: #ED9126 !important;
  margin-right: 30upx;
  vertical-align: middle;

}
button::after {
  border: none;
}
