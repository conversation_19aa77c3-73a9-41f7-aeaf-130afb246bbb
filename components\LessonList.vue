<template>
	<view>
		<view class="lesson-list" v-for="unit in units" :key="unit.id || unit.course_id">
			<view class="unit-header" @click="toggleUnit(unit.id || unit.course_id)">
				<image class="unit-icon" :src="getArrowIcon(unit.id || unit.course_id)" />
				<text class="unit-title" :class="{'icon-color': comeFrom == 'learningGoal'}">{{ unit.course_name }}</text>
			</view>

			<view
				class="lesson-item"
				v-for="(lesson, index) in unit.classes"
				:key="lesson.class_id"
				@click="goPage(lesson)"
				v-show="expandedUnits[unit.id || unit.course_id]"
				
			>
				<text class="lesson-name text-ellipsis">{{ lesson.class_name }}</text>
				<image class="lesson-icon" :src="getIcon('right')" />
			</view>
		</view>
	</view>
</template>

<script>
import Tools from '/utils/index.js'

export default {
	name: 'LessonList',
	props: {
		comeFrom: {
			type: String,
		},
		units: {
			type: Array,
			required: true
		}
	},
	data() {
		return {
			expandedUnits: {}
		};
	},
	created() {
		// 初始化展开状态
		this.units.forEach(unit => {
			this.$set(this.expandedUnits, unit.id, true);
		});
	},
	methods: {
		toggleUnit(unitId) {
			this.$set(this.expandedUnits, unitId, !this.expandedUnits[unitId]);
		},
		getArrowIcon(unitId) {
			return this.expandedUnits[unitId] ? this.getIcon('up') : this.getIcon('down');
		},
		getIcon(type) {
			if(this.comeFrom == 'learningGoal') {
				return `/static/icons/arrow_${type}2.png`
			} else {
				return `/static/icons/arrow_${type}1.png`
			}
		},
		goPage(item) {
			if(this.comeFrom == 'learningGoal') {
				console.log("ppp", item)
				uni.navigateTo({
					url: `/pages/learningGoal/detail?classId=${item.class_id}`
				});
			} else {	
				Tools.setStorage('WORDLIST', item);
				uni.navigateTo({
					url: '/pages/wordSpell/wordList'
				});
			}
		}
	}
};
</script>

<style>
.lesson-list {
	background-color: #fff;
	border-radius: 20rpx;
	width: 100%;
	padding: 32rpx 30rpx;
	box-sizing: border-box;
	margin-top: 30rpx;
}

.unit-header {
	display: flex;
	align-items: center;
	cursor: pointer;
}

.unit-icon {
	width: 28rpx;
	min-width: 28rpx;
	height: 28rpx;
	margin-right: 20rpx;
	transition: transform 0.3s ease;
}

.unit-title {
	color: #f59c12;
	font-size: 28rpx;
	font-family: PingFang SC-Medium;
	font-weight: 500;
	&.icon-color {
		color: #FC6C0C;
	}
}

.lesson-item {
	background-color: #f3f3f3;
	border-radius: 20rpx;
	width: 562rpx;
	height: 80rpx;
	margin: 24rpx 0 0 78rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	box-sizing: border-box;
}

.lesson-number {
	color: #333;
	font-size: 28rpx;
	font-family: PingFang SC-Regular;
	margin-right: 20rpx;
}

.lesson-name {
	color: #333;
	font-size: 28rpx;
	font-family: PingFang SC-Medium;
	font-weight: 500;
	max-width: 200rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.lesson-icon {
	width: 28rpx;
	height: 28rpx;
	margin-left: auto;
}
</style> 