import Tools from '/utils/index.js'
export const baseUrl = "https://dev.tongbuxueapp.com/api";
// export const baseUrl = "https://pro.tongbuxueapp.com/api";

export default  function request (path, method, data, timeout = 200000) {
	return new Promise(async function(resolve, reject){
		uni.showNavigationBarLoading()
		await uni.request({
		    url: baseUrl + '/v2_0_0' + path, //仅为示例，并非真实接口地址。  	  
			header: {
				'Content-Type': 'application/json', //自定义请求头信息
				'C-TOKEN': Tools.getStorage('myShare_token') ? 'Bearer ' + Tools.getStorage('myShare_token') : Tools.md5Encrypt('wsqa6ewiuqwerfghjyu12oopiumnbvgd' + Tools.timestampToTime(new Date().getTime(), 1, 1)),
				'C-TIME': Tools.timestampToTime(new Date().getTime(), 1, 1),
				'C-MODEL': 'debug',
				'C-PID': '1',
				'C-CHANNEL': 'guanfang',
				'C-PACKNAME': encodeURIComponent('小学英语')
				// 'Referer': 'https://www.tongbuxueapp.com'
				// 'C-IMEI': '',
				// 'C-PHONE-VERSION': '',
				// 'C-APP-VERSION': '',
				// 'c-appleid': '',
			},		
			data: Tools.encrypt(JSON.stringify(data)), 
			method,
			timeout,		
			success: (res) => {
				if (res.statusCode === 200) {
					if (res.data) {
						res.data = JSON.parse(Tools.decrypt(res.data))
						if (res.data.code == '10003') {
							Tools.toast(res.data.message);
						}
					}
				}
				// else if (res.data.code === 50014 || res.data.code === 401 ) {
				// 	Tools.toast("登录失效,请重新登录 !");
				// 	uni.clearStorage();
				// 	store.commit('initToken');
				// 	store.commit('loginShow', true);
				// } else {
				// 	Tools.toast(res.data.message);
				// }
				uni.hideNavigationBarLoading();
				resolve(res);
			},
			fail: (res) => {
		     	uni.hideNavigationBarLoading();
				Tools.toast(res.data.message);
			}
		});
	}) 
}
	
	
