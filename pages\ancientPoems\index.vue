<template>
  <view class="ancient-poems">
    <!-- 年级选项卡 -->
    <scroll-view 
	v-if="grades && grades.length"
      scroll-x 
      class="grade-tabs"
      :scroll-with-animation="true"
      :show-scrollbar="false"
    >
      <view class="tabs-content">
        <view 
          v-for="grade in grades" 
          :key="grade.id"
          :class="['tab-item', { active: currentGrade === grade.id }]"
          @click="changeGrade(grade.id)"
        >
          {{ grade.name }}
        </view>
      </view>
    </scroll-view>

    <!-- 诗歌列表 -->
    <view class="poem-list">
      <view 
        v-for="poem in poems" 
        :key="poem.id" 
        class="poem-item"
		@click="handleClick(poem)"
      >
        <view class="poem-icon">
			<image class="img" src="/static/icons/gushi.png" />
		</view>
        <view class="poem-info">
          <view class="poem-title">{{ poem.title }}</view>
          <view class="poem-author">{{ poem.dynasty }} · {{ poem.author }}</view>
          <view class="poem-first-line">{{ poem.content }}</view>
        </view>
      </view>
    </view>
	<uni-load-more v-if="totalNum > 0" :status="loadStatus" :content-text="{ contentdown: '加载更多', }" @tap="loadMore"/>
  </view>
</template>

<script>
import http from '/api/index.js'
import Tools from '/utils/index.js'

export default {
  data() {
    return {
	  userkey: uni.getStorageSync('userkey'),
      currentGrade: '一年级',
      grades: [],
	  poems: [],
	  totalNum: 0,
	  loadStatus: '',
	  dataParams: {
	    limit: 20,
	    page: 1
	  }
    }
  },
  onLoad() {
  	this.getGrades()
	
  },
  onUnload() {
  	console.log(window.history.state)
  	if(!window.history.state.back) {
  		Tools.goBack()
  	}
  },
  onReachBottom(){
    console.log('页面滚动到底部');
  	this.loadMore()
  },
  methods: {
    getList() {
        this.loadStatus = 'loading'
		console.log(this.userkey)
        const params = {
            userkey: uni.getStorageSync('userkey'),
            grade: this.currentGrade,
            ...this.dataParams
        }
        
        console.log("入参：：：", params)
        http.getGushiList(params).then(res => {
            if (res.statusCode === 200) {
                console.log("出参：：：", res.data)
                const { lists, total } = res.data.data
                
                this.poems = this.poems.concat(...lists)
                this.totalNum = total
                this.loadStatus = this.poems?.length < total ? 'more' : 'noMore';
            }
        })
  	},
    getGrades() {
        const params = {
            userkey: uni.getStorageSync('userkey')
        }
        
        console.log("入参：：：", params)
        http.getGushiGradeList(params).then(res => {
            if (res.statusCode === 200) {
                console.log("出参：：：", res.data)
                this.grades = res.data.data
          this.getList()
            }
        })
    },
      loadMore(){
        if (this.loadStatus !== 'noMore') {
          this.dataParams.page += 1;
          console.log("PAGE:::", this.dataParams.page)
          this.getList()
        }
      },
    changeGrade(grade) {
      this.currentGrade = grade
      this.dataParams.page = 1
      this.poems = []
      this.getList()
    },
    handleClick(item) {
      uni.navigateTo({
        url: `/pages/ancientPoems/detail?id=${item.id}`
      })
	 
    }
  }
}
</script>

<style lang="scss" scoped>
.ancient-poems {
  min-height: 100vh;
  padding: 0 0 calc(env(safe-area-inset-bottom) + 30rpx);
  background-color: #f8f8f8;

  .grade-tabs {
    white-space: nowrap;
    background-color: #fff;
    
    .tabs-content {
      display: inline-flex;
      padding: 20rpx 24rpx;
      
      .tab-item {
		height: 48rpx;
		line-height: 48rpx;
        padding: 0 28rpx;
        font-size: 28rpx;
        color: #999;
        border-radius: 30rpx;
        flex-shrink: 0;

        &:last-child {
          margin-right: 0;
        }

        &.active {
          color: #fff;
          background: #267BE5;
        }
      }
    }
  }

  .poem-list {
    padding: 28rpx 24rpx 0;

    .poem-item {
      display: flex;
	  align-items: center;
      padding: 22rpx;
	  margin-bottom: 28rpx;
	  border-radius: 20rpx;
	  background-color: #fff;

      .poem-icon {
        width: 196rpx;
        min-width: 196rpx;
        height: 176rpx;
        margin-right: 38rpx;
		.img {
			width: 100%;
			height: 100%;
		}
      }

      .poem-info {
        flex: 1;

        .poem-title {
          font-size: 32rpx;
		  font-weight: 500;
          color: #333;
          margin-bottom: 12rpx;
        }

        .poem-author {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 18rpx;
        }

        .poem-first-line {
          font-size: 24rpx;
          color: #267BE5;
        }
      }
    }
  }
}
</style>