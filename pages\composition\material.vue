<template>
  <view class="composition-page">
    <!-- 顶部搜索区域 -->
    <view class="search-header">
	  <uni-icons type="left" size="24" color="#666" @click="goBack"></uni-icons>
      <!-- <image src="/static/icons/arrow_right.png" class="back-icon" mode="aspectFit"  @click="goBack"></image> -->
      <view class="search-box" @click="goSearch">
      	<image src="/static/icons/search_gray.png" class="search-icon"></image>
		<view class="search-input">
          <input type="text" placeholder="搜索作文名称" readonly placeholder-class="placeholder-style"/>
		</view>
      </view>
      <uni-icons type="star-filled" size="20" color="#F4A316" @click="goCollect"></uni-icons>
    </view>

    <!-- 顶部标签导航 -->
    <view class="tab-nav">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab-item', activeTab === index ? 'active' : '']"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 年级选择 -->
    <scroll-view scroll-x class="grade-scroll" show-scrollbar="false">
      <view class="grade-list">
        <view 
          v-for="(grade, index) in grades" 
          :key="index"
          :class="['grade-item', activeGrade === index ? 'active' : '']"
          @tap="switchGrade(index)"
        >
          {{ grade }}
        </view>
      </view>
    </scroll-view>

    <!-- 作文列表 -->
    <view class="composition-list">
      <view class="composition-item" v-for="(item, index) in compositions" :key="index" @click="goDetail(item.id)">
        <view class="title">{{ item.title }}</view>
        <view class="content">{{ item.content }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import Tools from '/utils/index.js'

// 顶部标签数据
const tabs = ref(['作文体裁'])
const activeTab = ref(0)

// 年级数据
const grades = ref(['写人', '写景', '读后感', '写物', '抒情', '想象', '童话', '书信', '议论文', '日记', '周记', '叙事', '节日', '说明文', '动物', '记叙文', '续写', '话题', '励志', '植物'])
const activeGrade = ref(0)

// 作文列表数据
const compositions = ref([
  {
    title: '去水果店买水果',
    content: '今天，妈妈带我一起去水果店买水果，水果店里的水果真多啊！有甜甜的草莓，有又大又圆...'
  },
  {
    title: '六一儿童节日记50字',
    content: '今天虽然不是六一儿童节，但是校长让我们提前庆祝节日，我们在六一会演，但愿也能开心！'
  },
  {
    title: '感谢爸爸',
    content: '亲爱的爸爸！在我心中，您对我的爱如铜铃一般，震撼如钢，您对我的爱如同步伐留一般细心。'
  },
  {
    title: '白云',
    content: '天空好像大海，白云就像大海里的浪花。白云飘在蓝天上，像一朵公仔花，拔着拔着就大大...'
  },
  {
    title: '爱我的奶奶',
    content: '啊，第一个伟大的人啊！没有奶奶，就没有爸爸，也没有我。'
  }
])

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 切换年级
const switchGrade = (index) => {
  activeGrade.value = index
}

const goSearch = () => {
	Tools.goPage(1, `/pages/search/index`);
}

const goCollect = () => {
	Tools.goPage(1, `/pages/collect/index`);
}

const goDetail = (id) => {
	Tools.goPage(1, `/pages/composition/detail?id=${id}`)
}
</script>

<style scoped lang="scss">
.composition-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.tab-nav {
  display: flex;
  padding: 20rpx 30rpx;
}

.tab-item {
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  background-color: #EFEFEF;
  border-radius: 80rpx;
  font-size: 28rpx;
  color: #333333;
}

.tab-item.active {
  background-color: #FFF8EC;
  color: #F4A316;
}

.grade-scroll {
  width: calc(100% - 48rpx);
  padding: 10rpx 0;
  border: 2rpx solid #F4A316;
  border-radius: 20rpx;
  box-shadow: 0 0 20rpx 0 rgba(240,151,13,0.16);
  background-color: #fff;
  margin: 0 24rpx;
  white-space: nowrap;
}

.grade-list {
  display: inline-flex;
  padding: 0 30rpx;
}

.grade-item {
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #333333;
}

.grade-item.active {
  color: #F4A316;
}

.composition-list {
  padding: 20rpx;
}

.composition-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.composition-item .title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.composition-item .content {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 28rpx;
  color: #888888;
}

/* 新增顶部搜索样式 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx 0 20rpx;
}

.back-icon {
	width: 40rpx;
	height: 40rpx;
	transform: rotate(180deg);
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  margin: 0 20rpx;
  .search-icon {
  	width: 37rpx;
  	height: 36rpx;
  }
  .search-input {
  	flex: 1;
    display: flex;
    align-items: center;
    background-color: #ffffff;
	font-size: 28rpx;
	margin-left: 12rpx;
  	::v-deep uni-input {
  		width: 100%;
  	}
  }
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}
</style>