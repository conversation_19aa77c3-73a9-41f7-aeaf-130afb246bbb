import request from './request.js';

const http = {
	getCompositionCategory (data) {
		return request('/zuowen/get_category', 'POST', data)
	},
	addBookrack (data) {
		return request('/account/add_bookrack', 'POST', data)
	},
	
	removeBookrack (data) {
		return request('/account/remove_bookrack', 'POST', data)
	},

	getCompositionBookList (data) {
		return request('/account/zuowen_bookrack', 'POST', data)
	},
	getCompositionList (data) {
		return request('/zuowen/get_zuowen_lists', 'POST', data)
	},
	getSubCategory (data) {
		return request('/zuowen/get_sub_category', 'POST', data)
	},
	getCompositionDetail (data) {
		return request('/zuowen/get_zuowen_detail', 'POST', data)
	},
	getBihuaCategory (data) {
		return request('/bihua/get_category', 'POST', data)
	},
	getBooklists (data) {
		return request('/bihua/get_booklists', 'POST', data)
	},
	getHanziDetail (data) {
		return request('/tingxie/get_hanzi_detail', 'POST', data)
	},
	getCiyuDetail (data) {
		return request('/tingxie/get_ciyu_detail', 'POST', data)
	},
	addClick (data) {
		return request('/bihua/add_click', 'POST', data)
	},
	getAiZuoWen (data) {
		return request('/ai/zuowen', 'POST', data)
	},
	getAiZuoWenToken (data) {
		return request('/ai/get_token', 'POST', data)
	},
	getGuoxueList (data) {
		return request('/guoxue/list', 'POST', data)
	},
	getGuoxueZhangjieList (data) {
		return request('/guoxue/zhangjie_list', 'POST', data)
	},
	getGuoxueZhangjieDetail (data) {
		return request('/guoxue/zhangjie_detail', 'POST', data)
	},
	getXuexiMubiaoBookList (data) {
		return request('/xueximubiao/book_list', 'POST', data)
	},
	getXuexiMubiaoCourseList (data) {
		return request('/xueximubiao/course_list', 'POST', data)
	},
	getXuexiMubiaoDetail (data) {
		return request('/xueximubiao/detail', 'POST', data)
	},
	getGushiGradeList (data) {
		return request('/gushi/grade_list', 'POST', data)
	},
	getGushiList (data) {
		return request('/gushi/list', 'POST', data)
	},
	getGushiDetail (data) {
		return request('/gushi/detail', 'POST', data)
	},
	getBookDetail (data) {
		return request('/course/get_click_lists', 'POST', data)
	},
	getTakeTest(data) {
		return request('/xkw/get_xt', 'POST', data)
	},
	submitQuestion(data) {
		return request('/xkw/sumbit_question', 'POST', data)
	},
	getSentencenUits(data) {
		return request('/sentence/get_sentence_units', 'POST', data)
	},
	



	
	

}
export default http;