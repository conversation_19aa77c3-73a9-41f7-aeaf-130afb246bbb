<template>
  <view class="container">
    <view class="navbar">
      <view
        v-for="(item, index) in navItems"
        :key="index"
        :class="['nav-item', { active: currentIndex === index }]"
        @click="handleSwitch(index)"
      >
        {{ item }}
      </view>
    </view>
    <view v-if="detailLists[currentIndex]?.content" class="content-box">
      <rich-text :nodes="formatRichText(detailLists[currentIndex]?.content)" class="rich-content"></rich-text>
    </view>
    <view v-else class="no-data">
      暂无内容
    </view>
  </view>
</template>

<script>
import http from '/api/index.js'
import Tools from '/utils/index.js'

export default {
  name: 'LearningGoalDetail',
  data() {
    return {
      classId: '',
      currentIndex: 0,
      detailLists: []
    }
  },
  computed: {
    navItems() {
      return this.detailLists.map(item => {
        // 如果标题包含HTML实体编码，需要解码
        if (item.title && typeof item.title === 'string') {
          return item.title
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&amp;/g, '&')
            .replace(/&nbsp;/g, ' ')
            // 移除所有HTML标签
            .replace(/<[^>]+>/g, '');
        }
        return item.title || '';
      }) || [];
    }
  },
  onLoad(options) {
		this.classId = options.classId
		this.getDetails()
	},
  methods: {
    handleBack() {
      uni.navigateBack({
        delta: 1
      })
    },
    handleSwitch(index) {
      if (this.currentIndex === index) return
      this.currentIndex = index
      // 这里可以根据切换添加其他逻辑，比如更新页面内容
    },
    getDetails() {
      const params = {
        userkey: uni.getStorageSync('userkey'),
        class_id: this.classId
      }
      console.log("入参：：：", params)

      http.getXuexiMubiaoDetail(params).then(res => {
        if (res.statusCode === 200) {
          console.log("出参：：：", res.data.data)
          if(res.data.code === 80004) {
            Tools.toast(res.data.message);
            return;
          }
          this.detailLists = res.data.data?.detail;
          // 详细打印内容格式，帮助调试
          if (this.detailLists?.[0]?.content) {
            console.log('Content type:', typeof this.detailLists[0].content);
            console.log('Content value:', this.detailLists[0].content);
            console.log('Formatted content:', this.formatRichText(this.detailLists[0].content));
          }
        }
      })
    },
    formatRichText(content) {
      if (!content) return '';
      
      try {
        let decodedContent = content
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&amp;/g, '&')
          .replace(/&nbsp;/g, ' ');
        
        // 检查是否是形近字页面
        const isXingJinZiPage = (
          (
            this.detailLists[this.currentIndex]?.title?.includes('形近字') ||
            decodedContent.includes('形近字') ||
            decodedContent.includes('class="contain_pinyin"')
          )
        );
        
        // 如果是形近字页面，添加图片
        if (isXingJinZiPage) {
          decodedContent = decodedContent.replace(
            /<div class="bracket"><\/div>/g,
            '<div class="bracket"><img class="img" src="/static/icons/bracket.png" style="width:100%;height:100%;" /></div>'
          );
        }
        
        console.log('Decoded content:', decodedContent);
        return decodedContent;
      } catch (error) {
        console.error('Format rich text error:', error);
        return '';
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding: 0 0 calc(env(safe-area-inset-bottom) + 30rpx);
  background-color: #f8f8f8;
}

.navbar {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding-bottom: 14rpx;
  .nav-item {
    padding: 24rpx 0 10rpx;
    font-size: 28rpx;
    color: #999;
    &.active {
      font-weight: 500;
      color: #FC6C0C;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 6rpx;
        border-radius: 44rpx;
        background-color: #FC6C0C;
      }
    }
  }
}
.content-box {
  padding: 40rpx 22rpx;
  
  :deep(.rich-content) {
    .contain_row {
      margin-bottom: 32rpx;
      line-height: 1.6;
      font-size: 32rpx;
      color: #333;
      position: relative;
    }

    .contain_title {
      margin: 40rpx 0;
      font-size: 32rpx;
      color: #000;
      &:first-child {
        margin-top: 0;
      }
    }
    
    .contain_pinyin {
      padding-left: 24rpx;
      margin-bottom: 60rpx;
      display: flex;
      align-items: center;
      
      .bracket {
        width: 36rpx;
        height: 140rpx;
        margin: 0 28rpx 0 20rpx;
        
        .img {
          width: 100%;
          height: 100%;
        }
      }
      
      .bracket_right {
        height: 180rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        div {
          font-size: 32rpx;
          color: #000;
        }
        
        .pinyin_row {
          font-size: 32rpx;
          color: #000;
        }
      }
    }

    .sentence {
      display: inline-block;
      font-size: 32rpx;
      color: #000;
    }
    
    .point {
      display: inline-block;
      padding: 4rpx 26rpx;
      background-color: #FC6C0C;
      border-radius: 44rpx;
      color: #fff;
      font-size: 28rpx;
      line-height: 40rpx;
      vertical-align: middle;
    }
    
    .red {
      display: inline;
      color: #FC6C0C;
    }
    
    .opposite, .opposite2 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      div {
        font-size: 32rpx;
        color: #000;
      }
    }
  }
}
.no-data {
	height: 50vh;
	line-height: 50vh;
	color: #666;
	text-align: center;
	font-size: 32rpx;  
}
</style>