<template>
  <view class="composition-page">
    <!-- 顶部搜索区域 -->
    <view class="search-header">
	  <uni-icons type="left" size="24" color="#666" @click="goBack"></uni-icons>
      <!-- <image src="/static/icons/arrow_right.png" class="back-icon" mode="aspectFit"  @click="goBack"></image> -->
      <view class="search-box" @click="goSearch">
      	<image src="/static/icons/search_gray.png" class="search-icon"></image>
		<view class="search-input">
          <input type="text" placeholder="搜索作文名称" :value="searchText" readonly placeholder-class="placeholder-style"/>
		</view>
      </view>
      <uni-icons type="star-filled" size="20" color="#F4A316" @click="goCollect"></uni-icons>
    </view>

    <!-- 顶部标签导航 -->
	<scroll-view scroll-x class="tab-scroll" show-scrollbar="false">
		<view class="tab-nav">
		  <view 
			v-for="(tab, index) in tabs" 
			:key="index"
			:class="['tab-item', activeTab === tab.id ? 'active' : '']"
			@click="switchTab(tab.id)"
		  >
			{{ tab.name }}
		  </view>
		</view>
	</scroll-view>

    <!-- 年级选择 -->
    <scroll-view scroll-x class="grade-scroll" show-scrollbar="false">
      <view class="grade-list">
        <view 
          v-for="(grade, index) in grades" 
          :key="index"
          :class="['grade-item', activeGrade === grade.id ? 'active' : '']"
          @click="switchGrade(grade.id)"
        >
          {{ grade.name }}
        </view>
      </view>
    </scroll-view>

    <!-- 作文列表 -->
    <view class="composition-list">
      <view class="composition-item" v-for="(item, index) in compositions" :key="index" @click="goDetail(item.id)">
        <view class="title">{{ item.title }}</view>
        <view class="content">{{ item.content }}</view>
      </view>
    </view>
	<uni-load-more v-if="totalNum > 0" :status="loadStatus" :content-text="{ contentdown: '加载更多', }" @tap="loadMore"/>
  </view>
</template>

<script setup>
import { ref, reactive, onBeforeMount, onMounted, onUnmounted } from 'vue'
import { onReachBottom } from '@dcloudio/uni-app'
import Tools from '/utils/index.js'
import http from '/api/index.js'

// 顶部标签数据
let tabs = ref([]), activeTab = ref('1'), grades = ref([]), activeGrade = ref(''), compositions = ref([]), totalNum = ref(0), dataParams = reactive({ page: 1, limit: 20, search: '' }), loadStatus = ref('')

// Add searchText ref
const searchText = ref('')

onBeforeMount(() => {
  getCompositionCategory()
})

onMounted(() => {
  uni.$on('updateSearch', handleSearchUpdate)
})

onUnmounted(() => {
  uni.$off('updateSearch', handleSearchUpdate)
})

onReachBottom(() => {
    console.log('页面滚动到底部');
	loadMore()
});

const loadMore = () =>{
	if (loadStatus.value !== 'noMore') {
		dataParams.page += 1;
		console.log("PAGE:::", dataParams.page)
		getCompositionList();
	}
}

const initList = () =>{
	dataParams.page = 1
	compositions.value = []
	getCompositionList()
}

const getGrades = () =>{
	let result = []
	
	tabs.value && tabs.value.length && tabs.value.map(item => {
		if(activeTab.value == item.id) {
			result = item.children
		}
	})
	activeGrade.value = result && result.length && result[0].id || ''
	console.log("grades>>>", result)
	return result
}

const getCompositionCategory = () => {
	http.getCompositionCategory({
		userkey: uni.getStorageSync('userkey')
	}).then(res => {
		if (res.statusCode === 200) {
			if (res.data.data && res.data.data.length) {
				tabs.value = res.data.data
				if (tabs.value && tabs.value.length) {
					grades.value = getGrades()
					switchTab(tabs.value[0].id)
				}
			}
		}
	})
}

const getCompositionList = () => {
	loadStatus.value = 'loading'
	const params = {
		userkey: uni.getStorageSync('userkey'),
		category_id: activeGrade.value,
		type: 1,
		search: searchText.value,
		...dataParams
	}
	
	console.log("入参：：：", params)
	http.getCompositionList(params).then(res => {
		if (res.statusCode === 200) {
			console.log("出参：：：", res.data)
			const { lists, total } = res.data.data
			
			compositions.value = compositions.value.concat(...lists)
			totalNum.value = total
			loadStatus.value = compositions.value.length < total ? 'more' : 'noMore';
		}
	})
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 切换顶部标签
const switchTab = (id) => {
  activeTab.value = id
  grades.value = getGrades()
  initList()
}

// 切换年级
const switchGrade = (id) => {
  activeGrade.value = id
  initList()
}

const goSearch = () => {
	Tools.goPage(1, `/pages/search/index`);
}

const goCollect = () => {
	Tools.goPage(1, `/pages/collect/index`);
}

const goDetail = (id) => {
	Tools.goPage(1, `/pages/composition/detail?id=${id}`)
}

// Add handler for search updates
const handleSearchUpdate = (text) => {
  searchText.value = text
  dataParams.search = text
  initList()
}
</script>

<style scoped lang="scss">
.composition-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.tab-scroll {
	width: calc(100% - 48rpx);
	white-space: nowrap;
}

.tab-nav {
  display: flex;
  padding: 20rpx 30rpx;
}

.tab-item {
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  background-color: #EFEFEF;
  border-radius: 80rpx;
  font-size: 28rpx;
  color: #333333;
}

.tab-item.active {
  background-color: #FFF8EC;
  color: #F4A316;
}

.grade-scroll {
  width: calc(100% - 48rpx);
  padding: 10rpx 0;
  border: 2rpx solid #F4A316;
  border-radius: 20rpx;
  box-shadow: 0 0 20rpx 0 rgba(240,151,13,0.16);
  background-color: #fff;
  margin: 0 24rpx;
  white-space: nowrap;
}

.grade-list {
  display: inline-flex;
  padding: 0 30rpx;
}

.grade-item {
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #333333;
}

.grade-item.active {
  color: #F4A316;
}

.composition-list {
  padding: 20rpx 20rpx 0;
}

.composition-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.composition-item .title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.composition-item .content {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 28rpx;
  color: #888888;
}

/* 新增顶部搜索样式 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx 0 20rpx;
}

.back-icon {
	width: 40rpx;
	height: 40rpx;
	transform: rotate(180deg);
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  margin: 0 20rpx;
  .search-icon {
  	width: 37rpx;
  	height: 36rpx;
  }
  .search-input {
  	flex: 1;
    display: flex;
    align-items: center;
    background-color: #ffffff;
	font-size: 28rpx;
	margin-left: 12rpx;
  	::v-deep uni-input {
  		width: 100%;
  	}
  }
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}
</style>