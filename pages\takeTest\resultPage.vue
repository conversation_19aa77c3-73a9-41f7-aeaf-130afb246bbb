<template>
    <view class="result-container">
        <!-- 顶部恭喜区域 -->
        <view class="congratulation-section">
            <view class="badge-container">
                    <image class="thumbs-up-icon" src="/static/icons/result_bg1.png" mode="aspectFit"></image>
            </view>
            <view class="congrat-text">
                <text class="main-text">恭喜~</text>
                <text class="sub-text">同步练习已完成</text>
            </view>
        </view>

        <!-- 白色卡片区域 -->
        <view class="result-card">
            <!-- 昆虫备忘录标题 -->
            <view class="record-header">
                <text class="record-title">{{pageData?.title}}</text>
                <view class="record-stats">
                    <text class="stats-text">共{{ totalQuestions }}题</text>
                    <text class="stats-text">答对{{ correctCount }}题</text>
                    <text class="stats-text">用时{{ timeUsed }}</text>
                </view>
            </view>

            <!-- 题目列表 -->
            <view class="question-list">
                <view @click="goToQuestion(index + 1)" v-for="(item, index) in questionResults" :key="index" class="question-item">
                    <view class="question-info" >
                        <text class="question-number">第{{ index + 1 }}题</text>
                        <text class="question-type" :class="getQuestionTypeClass(item.status)">{{ item.status }}</text>
                    </view>
                    <view class="question-right">
                        <!-- <view class="question-status" :class="getStatusClass(item.status)">
                            <text class="status-text">{{ getStatusText(item.status) }}</text>
                        </view> -->
                        <view class="question-arrow">
                          <image class="arrow-icon" src="/static/icons/arrow_right2.png" mode="aspectFit"></image>

                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部按钮 -->
        <view class="bottom-buttons">
            <view class="error-collect-btn">
                <text class="btn-text">错题集</text>
                <view class="error-badge">
                    <text class="badge-text">+{{ totalQuestions - correctCount}}</text>
                </view>
            </view>
            <view class="retry-btn" @click="retryTest">
                <text class="btn-text">重做一遍</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
			pageData: '',
            answerRecords: {},
            questionList: [],
            totalQuestions: 0, // 动态题目数
            correctCount: 0,
            timeUsed: '',
            questionResults: [],
            questionTypes: []
        };
    },
    onLoad(data) {
		this.pageData = data;
        this.loadTestResults();
    },
    methods: {
        loadTestResults() {
            this.answerRecords = uni.getStorageSync('TEST_ANSWER_RECORDS') || {};
            this.questionList = uni.getStorageSync('TEST_QUESTION_LIST') || [];
            
            // 获取测试用时
            const testStartTime = uni.getStorageSync('TEST_START_TIME');
            const testEndTime = uni.getStorageSync('TEST_END_TIME');
            
            if (testStartTime && testEndTime) {
                const timeUsedSeconds = Math.floor((testEndTime - testStartTime) / 1000);
                this.timeUsed = this.formatTimeUsed(timeUsedSeconds);
            }
            
            if (this.questionList.length > 0) {
                this.totalQuestions = this.questionList.length;
                this.calculateResults();
            }
        },
        
        calculateResults() {
            let correct = 0;
            this.questionResults = [];
            this.totalQuestions = this.questionList.length;
            for (let i = 1; i <= this.totalQuestions; i++) {
                const question = this.questionList[i - 1];
                let status = '未作答';
                if (question.stem && question.stem.type === '复合题') {
                    // 复合题：所有小题都答对才算正确
                    let allAnswered = true;
                    let allCorrect = true;
                    const subCount = question.stem.sqs.length;
                    for (let j = 0; j < subCount; j++) {
                        const subKey = `${i}_${j}`;
                        const record = this.answerRecords[subKey];
                        const answerOption = question.answer.anSqs[j]?.ans[0]?.html;
                        if (!record || !record.hasAnswered) {
                            allAnswered = false;
                            allCorrect = false;
                            break;
                        }
                        if (record.selectedOption !== answerOption) {
                            allCorrect = false;
                        }
                    }
                    if (!allAnswered) {
                        status = '未作答';
                    } else if (allCorrect) {
                        status = '正确';
                        correct++;
                    } else {
                        status = '错误';
                    }
                } else {
                    // 普通题
                    const record = this.answerRecords[i];
                    if (record && record.hasAnswered) {
                        if (record.selectedOption === question.answerOption) {
                            status = '正确';
                            correct++;
                        } else {
                            status = '错误';
                        }
                    }
                }
                this.questionResults.push({ status: status });
            }
            this.correctCount = correct;
        },
        
        getQuestionType(index) {
            const types = ['未作答', '正确', '错误', '正确', '正确'];
            return types[index] || '未作答';
        },
        
        getQuestionTypeClass(status) {
            switch (status) {
                case '正确': return 'type-correct';
                case '错误': return 'type-wrong';
                default: return 'type-unanswered';
            }
        },
        
        getStatusText(status) {
            return status;
        },
        
        retryTest() {
            // 清除答题记录
            uni.removeStorageSync('TEST_ANSWER_RECORDS');
            
            // 返回到测试页面，传递重做标识
            uni.navigateBack({ 
                delta: 1,
                success: () => {
                    // 通过事件通知测试页面重置状态
                    uni.$emit('retryTest');
                }
            });
        },
        
        formatTimeUsed(seconds) {
            const minutes = Math.floor(seconds / 60);
            const secs = seconds % 60;
            
            if (minutes > 0) {
                return `${minutes}分${secs}秒`;
            } else {
                return `${secs}秒`;
            }
        },
        goToQuestion(questionNumber) {
            // 保存要跳转的题目编号
            uni.setStorageSync('JUMP_TO_QUESTION', questionNumber);
            
            // 返回到测试页面
            uni.navigateBack({ 
                delta: 1,
                success: () => {
                    // 通过事件通知测试页面跳转到指定题目
                    uni.$emit('jumpToQuestion', questionNumber);
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.result-container {
    min-height: 100vh;
    // background: linear-gradient(180deg, #7ED321 0%, #A8E063 100%);
    background: #72CF67;
    padding: 0;
    position: relative;
    padding-bottom: 20px;

}

.congratulation-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 80rpx 140rpx 0 0;
    gap: 30rpx;
    position: relative;
    z-index: 2;
    margin-bottom: -30rpx;
    
    .badge-container {
        .thumbs-up-icon {
            width: 200upx;
            height: 200upx;
        }
    }
    
    .congrat-text {
        .main-text {
            display: block;
            font-size: 52rpx;
            font-weight: bold;
            color: #fff;
            margin-bottom: 12rpx;
        }
        
        .sub-text {
            font-size: 30rpx;
            color: #fff;
            opacity: 0.95;
        }
    }
}

.result-card {
    background: #fff;
    margin: 0 30rpx;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    padding-top: 20rpx;
}

.record-header {
    padding: 40rpx 30rpx 30rpx 30rpx;
    // border-bottom: 1rpx solid #f5f5f5;
    text-align: center;
    
    .record-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 24rpx;
    }
    
    .record-stats {
        display: flex;
        gap: 40rpx;
        justify-content: center;
        
        .stats-text {
            font-size: 26rpx;
            color: #666;
        }
    }
}

.question-list {
        padding: 32rpx 30rpx;
        box-sizing: border-box;

    .question-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16rpx 15rpx;
        border: 1upx solid #DFDFDF;
        margin-bottom: 28upx;
        border-radius: 10px;

        // border-bottom: 1rpx solid #f0f0f0;
       
        
        .question-info {
            display: flex;
            align-items: center;
            gap: 20rpx;
            padding-left: 20rpx;
            
            .question-number {
                font-size: 30rpx;
                color: #333;
                font-weight: 500;
                width: 100upx;
            }
            
            .question-type {
                font-size: 26rpx;
                
                &.type-correct {
                    color: #7ED321; // 正确
                }
                
                &.type-wrong {
                    color: #FF6B6B; // 错误
                }
                
                &.type-unanswered {
                    color: #999; // 未作答
                }
            }
        }
        
        .question-right {
            display: flex;
            align-items: center;
            gap: 20rpx;
            margin-right: 15upx;
            
            .question-arrow {
                .arrow-icon {
                    width: 32upx;
                    height: 32upx;
                    color: #FF8C00;
                    font-weight: bold;
                }
            }
        }
        
      
    }
}



.bottom-buttons {
    display: flex;
    gap: 24rpx;
    margin: 60rpx 30rpx 40rpx 30rpx;
    
    .error-collect-btn {
        width: 160rpx;
        height: 72rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #FFFFFF;
        border: 2rpx solid rgba(255, 255, 255, 0.8);
        position: relative;
        
        .btn-text {
            font-size: 30rpx;
            font-weight: 500;
            color: #333333;
        }
        
        .error-badge {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            width: 40rpx;
            height: 40rpx;
            background: #FF4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            
            .badge-text {
                font-size: 20rpx;
                color: #fff;
                font-weight: bold;
            }
        }
    }
    
    .retry-btn {
        flex: 1;
        height: 72rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
        
        .btn-text {
            font-size: 30rpx;
            font-weight: 500;
            color:  #F59C12;
        }
    }
}
</style>






















