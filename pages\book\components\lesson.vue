<template>
	<!-- animate__animated animate__lightSpeedInRight -->
	<!-- animate__pulse -->
	<!-- animate__backInRight -->
	<view class="lesson-overlay animate__animated" :class="{ animate__fadeOut: isClosing }" @click.self="closeLesson">
		<view :class="{ closing: isClosing }" class="lesson-container">
			<view class="header">
				<text class="title">目录</text>
				<uni-icons @click="closeLesson" class="close-icon" type="closeempty" color="#999999" size="28"></uni-icons>
				<!-- <image   src="/static/images/readBook/close.png" /> -->
			</view>

			<scroll-view class="content" scroll-y>
				<template v-for="(lesson, index) in lessons" :key="index">
					<!-- animate__bounceIn -->
					<view @click="jumpPage(lesson.beginPage, index)" class="unit-item animate__animated animate__bounceIn">
						<text class="unit-text">{{ lesson.unit }}</text>
						<!-- <text class="page-text">第{{ lesson.beginPage}}页</text> -->
						<text v-if="index < 2 || isVip" class="page-text">第{{ lesson.beginPage - lessons[0].beginPage + 1 }}页</text>
						<uni-icons v-else-if="!isVip" @click="closeLesson" class="locked" type="locked-filled" color="#999999" size="24"></uni-icons>
					</view>
				</template>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		lessons: {
			type: Array,
			default: () => []
		},
		isVip: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			isClosing: false
		};
	},
	methods: {
		closeLesson() {
			this.isClosing = true;
			setTimeout(() => {
				this.$emit('close');
			}, 300);
		},
		jumpPage(page, index) {
			if (!this.isVip && index > 1) {
				if (/(Android)/i.test(navigator.userAgent)) { //判断Android
					callNative.openVip();
				}
				else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) { //判断iPhone|iPad|iPod|iOS
					window.webkit.messageHandlers.openVip.postMessage(null);
				}
				
				this.closeLesson();
				return;
			}
			this.$emit('jumpPage', page);
		}
	}
};
</script>

<style scoped>
.lesson-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999999;
	display: flex;
	justify-content: flex-end;
	animation-duration: 0.4s; /* don't forget to set a duration! */
}

.lesson-container.closing {
	transform: translateX(100%);
}

.lesson-container {
	width: 580rpx;
	height: 100%;
	background: #ffffff;
	padding: 60rpx 30rpx 45rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	animation: slideIn 0.4s ease-out;
	border-top-left-radius: 30px;
	border-bottom-left-radius: 30px;
}

@keyframes slideIn {
	from {
		transform: translateX(100%);
	}
	to {
		transform: translateX(0);
	}
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #eeeeee;
}

.title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.close-icon {
	width: 40rpx;
	height: 40rpx;
}

.content {
	flex: 1;
	overflow-y: auto;
	margin-top: 20rpx;
}

.unit-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 20rpx;
	background: #f8f8f8;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
}

.unit-text {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
}

.page-text {
	font-size: 28rpx;
	color: #ff9f00;
	margin-left: 20rpx;
}
.locked {
	margin-left: 20rpx;
}
</style>
