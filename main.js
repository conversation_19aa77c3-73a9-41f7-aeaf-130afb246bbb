import App from './App'
import { createPinia } from 'pinia';
import 'animate.css';
import  VConsole  from  'vconsole';
let vConsole = new VConsole();

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import VueCompositionAPI from '@vue/composition-api'
Vue.use(VueCompositionAPI)

// import 'mathjax-full/es5/tex-mml-chtml';

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import uviewPlus from '@/uni_modules/uview-plus'

export function createApp() {
  const app = createSSRApp(App);
  app.use(uviewPlus);
  
  // 创建 Pinia 实例
  const pinia = createPinia();
  
  // 使用 Pinia
  app.use(pinia);

  return {
    app
  }
}
// #endif