<template>
  <view   v-if="currentNumber > 0" class="mask-container">
    <image 
      class="count-number" 
      :src="numberImages[currentNumber-1]" 
      mode="aspectFit"
    />
  </view>
</template>

<script>
export default {
  name: 'DownNum',
  data() {
    return {
      currentNumber: 3,
      showMask: true,
      timer: null,
      numberImages: [
        '/static/num1.png',
        '/static/num2.png',
        '/static/num3.png'
      ]
    }
  },
  mounted() {
    this.startCountdown()
  },
  methods: {
    startCountdown() {
      this.timer = setInterval(() => {
        if (this.currentNumber > 0) {
          this.currentNumber--
        } else {
          clearInterval(this.timer)
          this.showMask = false
          this.$emit('countdown-complete')
        }
      }, 1000)
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style scoped>
.mask-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.count-number {
  width: 200rpx;
  height: 200rpx;
  animation: scaleNumber 1s infinite;
}

@keyframes scaleNumber {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
