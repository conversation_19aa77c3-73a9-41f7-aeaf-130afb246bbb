<template>
	<template v-if="this.words.hanzi && this.words.hanzi.length">
	  <view v-if="type == 'wordList'" class="word-list">
		<!-- 课文信息区域 -->
		<view class="lesson-info">
		  <view class="lesson-title-wrap">
			<text class="lesson-title">{{ words.class_name }}</text>
		  </view>
		  
		  <!-- 词数和操作区 -->
		  <view class="word-count-bar">
			<text class="word-count">共 {{ words.hanzi && words.hanzi.length }} 词</text>
			<view class="select-all" @tap="toggleSelectAll">
			  <view class="checkbox" :class="{ checked: isAllSelected }"></view>
			  <text>{{isAllSelected?'取消全选': '全选'}}</text>
			</view>
			<text class="settings" @tap="openSettings">设置</text>
		  </view>

		  <!-- 词语网格 -->
		  <view class="word-grid">
			<view 
			  v-for="(word, index) in words.hanzi" 
			  :key="index"
			  class="word-item"
			  :class="{ selected: selectedWords.includes(word.id) }"
			  @tap="toggleWord(word.id)"
			>
			  <text>{{ word.word || word.phrase }}</text>
			</view>
		  </view>
		</view>

		<!-- 角色选择区域 -->
		<view class="role-selection">
		  <view class="role-card" @tap="goToSpell('parentSpell')">
			<text class="role-name">我是家长</text>
			<image class="role-avatar" src="/static/icons/parent-avatar.png" mode="aspectFit" />
		  </view>
		  <view class="role-card" @tap="goToSpell('studentSpell')">
			<text class="role-name">我是学生</text>
			<image class="role-avatar" src="/static/icons/student-avatar.png" mode="aspectFit" />
		  </view>
		</view>

		<!-- 底部提示区 -->
		<view class="bottom-tip">
		  <image class="tip-icon" src="/static/icons/horn.png" mode="aspectFit" />
		  <text class="tip-text">请准备好纸和笔，我们开始听写咯～</text>
		</view>

		<!-- 设置弹窗组件 -->
		<spell-set
		  v-if="showSettings" 
		  @close="closeSettings"
		/>
	  </view>
	  <parent-spell v-else-if="type == 'parentSpell'" ref="parentSpell"/>
	  <student-spell v-else ref="studentSpell"/>
	</template>
	<div class="no-data">暂无数据</div>
</template>

<script>
import Tools from '/utils/index.js'
import SpellSet from './components/spellSet.vue'
import ParentSpell from './parentSpell.vue'
import StudentSpell from './studentSpell.vue'

export default {
  components: {
    SpellSet,
	ParentSpell,
	StudentSpell
  },
  data() {
    return {
      words: {},
      selectedWords: [],
      showSettings: false,
	  type: 'wordList',
	  spellWords: {}
    }
  },
  computed: {
    isAllSelected() {
      return this.selectedWords.length === this.words.hanzi.length
    }
  },
  onUnload() {
	 this.reset()
	
  },
  watch: {
  	type(val) {
  		if (val === 'wordList') {
  			this.reset()
  		}
	}
  },
  created() {
	let item = Tools.getStorage('WORDLIST')
	
	if (item && item.ciyu) {
		item.hanzi = item.ciyu
	}
  	this.words = item;
	this.selectAll()
  },
  onBackPress(options) {
	  if(options && options.from == 'backbutton') { // 点击返回按钮
		  if (this.type != 'wordList') {
				this.type = 'wordList'
				return true // 阻止返回
		  } else {
			  return false // 允许返回
		  }
	  }
  },
  methods: {
	  reset() {
		  window.closeAudio();
		  // if (this.$refs && this.$refs.parentSpell) {
		  // 	this.$refs.parentSpell.stopAction();
		  // } else if (this.$refs && this.$refs.studentSpell) {
		  // 	this.$refs.studentSpell.stopAction();
		  // }
	  },
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedWords = []
      } else {
		this.selectAll()
      }
    },
	selectAll () {
		let idsArr = this.words.hanzi && this.words.hanzi.length && this.words.hanzi.map(item => item.id).filter(i => i)
		
		this.selectedWords = [...idsArr]
	},
    toggleWord(word) {
      const index = this.selectedWords.indexOf(word)
	  
      if (index === -1) {
        this.selectedWords.push(word)
      } else {
        this.selectedWords.splice(index, 1)
      }
    },
    openSettings() {
      this.showSettings = true
    },
    
    closeSettings() {
      this.showSettings = false
    },
    
    goToSpell(type) {
		if (!(this.selectedWords && this.selectedWords.length)) {
			Tools.toast('请选择字词');
			return
		}
		this.getSelectWords()
		this.type = type
		this.$nextTick(() => {
			this.$refs[type].initData(this.spellWords)
		})
    },
	getSelectWords() {
		let selectedWords = []
		
		this.selectedWords?.length && this.selectedWords.map(item => {
			this.words.hanzi.map(i => {
				if (i.id == item) {
					selectedWords.push(i)
				}
			})
		})
		this.spellWords = {
			...this.words,
			selectedWords
		}
		Tools.setStorage('WORDLIST_SPELL', this.spellWords)
	}
  }
}
</script>

<style lang="scss" scoped>
.word-list {
  min-height: 100vh;
  background-color: #F7F7F7;
  padding: 30rpx 24rpx;
}

.lesson-info {
  background-color: #FFF;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
}

.lesson-title-wrap {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .lesson-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    line-height: 32rpx;
  }
}

.word-count-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;

  .word-count {
    flex: 1;
    font-size: 24rpx;
    color: #999;
  }

  .select-all {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .checkbox {
      width: 20rpx;
      height: 20rpx;
      border: 2rpx solid #F59C12;
      border-radius: 50%;
      margin-right: 16rpx;
      position: relative;

      &.checked::after {
        content: '';
        position: absolute;
        width: 12rpx;
        height: 12rpx;
        background-color: #F59C12;
        border-radius: 50%;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    text {
      font-size: 24rpx;
      color: #333;
    }
  }

  .settings {
    flex: 1;
    font-size: 24rpx;
    color: #F59C12;
    text-align: right;
    cursor: pointer;
  }
}

.word-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 28rpx;

  .word-item {
    background-color: #F3F3F3;
    border-radius: 20rpx;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 30rpx;
    min-width: 70rpx;
    flex-grow: 0;
    flex-shrink: 0;
    transition: all 0.2s ease;

    &.selected {
      background-color: #FFECCC;
    }

    text {
      font-size: 36rpx;
      color: #333;
      font-family: 'Kaiti TC';
      font-weight: normal;
      white-space: nowrap;
      transition: color 0.2s ease;
    }
  }
}

.role-selection {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 40rpx;

  .role-card {
    background-color: #FFF;
    border-radius: 20rpx;
    width: 336rpx;
    height: 148rpx;
    display: flex;
    align-items: center;
    padding: 0 22rpx;
    box-shadow: 0 4rpx 0 rgba(245, 156, 18, 0.5);

    .role-name {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      font-family: PingFang SC;
	  margin-left:10px;
    }

    .role-avatar {
      width: 96rpx;
      height: 96rpx;
      margin-left: auto;
    }
  }
}

.bottom-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  margin-top: 40rpx;

  .tip-icon {
    width: 28rpx;
    height: 28rpx;
  }

  .tip-text {
    font-size: 26rpx;
    color: #F59C12;
    font-family: PingFang SC;
  }
}
.no-data {
	height: 50vh;
	line-height: 50vh;
	color: #666;
	text-align: center;
	font-size: 32rpx;  
}
</style>
